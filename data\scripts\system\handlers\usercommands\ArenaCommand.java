/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.LadderService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

/**
 * <AUTHOR>
 */
public class ArenaCommand extends UserCommand {
    public ArenaCommand() {
        super("arena");
    }

    public void executeCommand(Player player, String param) {
        @SuppressWarnings("unused")
        String[] params = param.split(" ");

        /*
         * announce(player, "This feature is currently disabled."); return;
         */

        if (!LadderService.getInstance().isInQueue(player)) {
            if (LadderService.getInstance().registerForArena(player)) {
                announce(player, "You have registered to the Arena queue!");
            }
            else {
                announce(player, "You have failed to register for the Arena queue.");
            }
        }
        else {
            LadderService.getInstance().unregisterFromQueue(player);
            announce(player, "You have unregistered from the Arena queue!");
        }
    }

    private void announce(Player player, String msg) {
        PacketSendUtility.sendSys2Message(player, "Arena", msg);
    }
}