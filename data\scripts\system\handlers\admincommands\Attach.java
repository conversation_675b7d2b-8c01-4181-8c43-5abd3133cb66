/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.controllers.NpcController;
import gameserver.controllers.NpcPlayerController;
import gameserver.controllers.effect.EffectController;
import gameserver.dataholders.DataManager;
import gameserver.dataholders.loadingutils.adapters.NpcEquipmentList;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Creature.FormationType;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.NpcPlayer;
import gameserver.model.gameobjects.StaticDoor;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.PlayerAppearance;
import gameserver.model.items.NpcEquippedGear;
import gameserver.model.items.PackageItem;
import gameserver.model.templates.spawn.SpawnGroup;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_DELETE;
import gameserver.network.aion.serverpackets.SM_ITEM_PACKAGE;
import gameserver.network.aion.serverpackets.SM_NPC_INFO;
import gameserver.services.OutpostService;
import gameserver.services.SerialService;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.utils.idfactory.IDFactory;
import gameserver.world.Executor;
import gameserver.world.NpcKnownList;
import gameserver.world.World;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 */
public class Attach extends AdminCommand {
    private static int followTargetObjectId = -1;

    public Attach() {
        super("attach");
    }

    @Override
    public void executeCommand(final Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_ADMIN) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command!");
            return;
        }

        // PlayerService.createTemporaryPlayer(admin);

        if (params.length > 0) {
            if ("column".equalsIgnoreCase(params[0])) {
                admin.setFormationType(FormationType.COLUMN);
            }
            else if ("triangle".equalsIgnoreCase(params[0])) {
                admin.setFormationType(FormationType.TRIANGLE);
            }
            else if ("bonusicon".equalsIgnoreCase(params[0])) {
                admin.setBonusIcon(Integer.parseInt(params[1]));
            }
            else if ("gatesiege".equalsIgnoreCase(params[0])) {
                OutpostService.getInstance().spawnGateSieges();
            }
            else if ("killer".equalsIgnoreCase(params[0])) {
                int level = Integer.parseInt(params[1]);

                SerialService.getInstance().setKillerLevel(admin, level);
            }
            else if ("guard".equalsIgnoreCase(params[0])) {
                int level = Integer.parseInt(params[1]);

                SerialService.getInstance().setGuardLevel(admin, level);
            }
            else if ("followtarget".equalsIgnoreCase(params[0])) {
                if (admin.getTarget() == null) {
                    PacketSendUtility.sendMessage(admin, "Please select a target.");
                    return;
                }

                followTargetObjectId = admin.getTarget().getObjectId();

                PacketSendUtility.sendMessage(admin, "Follow target set to "
                    + admin.getTarget().getName());
            }
            else if ("follow".equalsIgnoreCase(params[0])) {
                if (followTargetObjectId < 0) {
                    PacketSendUtility.sendMessage(admin, "Select a followtarget first.");
                    return;
                }
                else if (!(admin.getTarget() instanceof Creature)) {
                    PacketSendUtility.sendMessage(admin, "Please select a target.");
                    return;
                }

                Creature leader = (Creature) World.getInstance().findAionObject(
                    followTargetObjectId);
                Creature target = (Creature) admin.getTarget();

                if (leader == null) {
                    PacketSendUtility.sendMessage(admin, "Select a followtarget first.");
                    return;
                }

                if (leader.getFollowers().contains(target))
                    leader.removeFollower(target);
                else
                    leader.addFollower(target);
            }
            else if ("doors".equalsIgnoreCase(params[0])) {
                for (AionObject ao : admin.getKnownList().getObjects()) {
                    if (!(ao instanceof StaticDoor))
                        continue;

                    StaticDoor door = (StaticDoor) ao;

                    door.setOpen(!door.isOpen());

                    PacketSendUtility.sendMessage(admin, (door.isOpen() ? "Opened" : "Closed")
                        + " door id " + door.getObjectTemplate().getId() + ".");
                }
            }
            else if ("npcplayer".equalsIgnoreCase(params[0])) {
                PacketSendUtility.sendMessage(admin, "Spawning NPC Player");

                try {
                    SpawnTemplate st = SpawnEngine.getInstance().addNewSpawn(admin.getWorldId(),
                        admin.getInstanceId(), 1000001, admin.getX(), admin.getY(), admin.getZ(),
                        admin.getHeading(), 0, 0, true);

                    if (st != null)
                        PacketSendUtility.sendMessage(admin, "SpawnTemplate created");

                    NpcPlayer object = new NpcPlayer(IDFactory.getInstance().nextPlayerId(),
                        new NpcPlayerController(), st);

                    PlayerAppearance apperance = (PlayerAppearance) admin.getPlayerAppearance()
                        .clone();
                    object.setPlayerAppearance(apperance);

                    NpcEquipmentList equipList = NpcEquipmentList.createList(110101857, 111101671,
                        112101621, 113101683, 114101717, 100901400);
                    object.setEquipment(new NpcEquippedGear(equipList));

                    object.setPlayerClass(admin.getPlayerClass());
                    object.setGender(admin.getGender());

                    object.setName("Mirror of " + admin.getName());

                    if (object != null)
                        PacketSendUtility.sendMessage(admin, "NpcPlayer object created");

                    object.setKnownlist(new NpcKnownList(object));
                    object.setEffectController(new EffectController(object));

                    object.getController().onCreation();

                    PacketSendUtility.sendMessage(admin, "Spawning into world...");

                    World world = World.getInstance();
                    world.storeObject(object);
                    world.setPosition(object, st.getWorldId(), admin.getInstanceId(), st.getX(),
                        st.getY(), st.getZ(), st.getHeading());
                    world.spawn(object);

                    object.getController().onRespawn();

                    PacketSendUtility.sendMessage(admin, "Spawned NPC Player");
                }
                catch (Exception e) {
                    Logger.getLogger(getClass()).error(e);
                    PacketSendUtility.sendMessage(admin, "Error spawning NPC player");
                }
            }
            else {
                final Npc flag = createFlag(admin, Integer.parseInt(params[0]));

                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        destroyFlag(admin, flag);
                    }
                }, 5000);
            }
        }
        else {
            List<PackageItem> options = new ArrayList<PackageItem>();

            options.add(new PackageItem(187000121, 1));
            options.add(new PackageItem(187000129, 1));
            options.add(new PackageItem(187060103, 1));

            admin.setCurrentPackage(options);
            PacketSendUtility.sendPacket(admin, new SM_ITEM_PACKAGE(-1, options));
        }

        /*
         * if (admin.getTarget() instanceof Creature) { if (!admin.getFollowers().contains(admin.getTarget()))
         * admin.addFollower((Creature) admin.getTarget()); else admin.removeFollower((Creature) admin.getTarget()); }
         */

        // PlayerService.createTemporaryPlayer(admin);

        // PacketSendUtility.sendPacket(admin, new SM_QUIT_RESPONSE(true));
        // admin.getClientConnection().overrideActivePlayer(null);
        // CM_ENTER_WORLD.enterWorld(admin.getClientConnection(), admin.getObjectId(), true);

        /*
         * if ((admin.getTarget() == null || !(admin.getTarget() instanceof Player)) && params.length < 1) {
         * PacketSendUtility.sendMessage(admin,
         * "Syntax: //attach [name] -- either target a player or specify name to attach!"); return; } Player target =
         * null; if (params.length >= 1) target = World.getInstance().findPlayer(Util.convertName(params[0])); else if
         * (admin.getTarget() != null && admin.getTarget() instanceof Player) target = (Player) admin.getTarget(); if
         * (target == null) { PacketSendUtility.sendMessage(admin,
         * "No player with that name online or something went wrong!"); return; } PlayerService.playerLoggedOut(admin);
         * AionConnection connection = admin.getClientConnection(); admin.setClientConnection(null);
         * target.setSecondConnection(connection); World.getInstance().despawn(admin);
         */

        // connection.sendPacket(new SM_SKILL_LIST(target));

        // if (target.getSkillCoolDowns() != null)
        // connection.sendPacket(new SM_SKILL_COOLDOWN(target.getSkillCoolDowns()));

        // if (target.getItemCoolDowns() != null)
        // connection.sendPacket(new SM_ITEM_COOLDOWN(target.getItemCoolDowns()));

        // byte[] uiSettings = target.getPlayerSettings().getUiSettings();
        // byte[] shortcuts = target.getPlayerSettings().getShortcuts();

        // if (uiSettings != null)
        // connection.sendPacket(new SM_UI_SETTINGS(uiSettings, 0));
        // if (shortcuts != null)
        // connection.sendPacket(new SM_UI_SETTINGS(shortcuts, 1));

        /*
         * connection.sendPacket(new SM_CHANNEL_INFO(target.getPosition())); connection.sendPacket(new
         * SM_PLAYER_SPAWN(target)); connection.sendPacket(new SM_ABYSS_RANK(target.getAbyssRank()));
         */
    }

    private Npc createFlag(Player admin, int npcId) {
        SpawnTemplate spawn = new SpawnTemplate(admin.getX(), admin.getY(), admin.getZ(), (byte) 0,
            0, 0, 0);
        SpawnGroup spawnGroup = new SpawnGroup(admin.getWorldId(), npcId, 295, 1);
        spawn.setSpawnGroup(spawnGroup);
        spawnGroup.getObjects().add(spawn);

        final Npc flag = new Npc(IDFactory.getInstance().nextPlayerId(), new NpcController(),
            spawn, DataManager.NPC_DATA.getNpcTemplate(npcId));

        World.getInstance().setPosition(flag, admin.getWorldId(), admin.getInstanceId(),
            admin.getX(), admin.getY(), admin.getZ(), (byte) 0);

        flag.setOutpostFlag(true);

        admin.getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                PacketSendUtility.sendPacket(pl, new SM_NPC_INFO(flag, pl));
                return true;
            }
        }, true);

        return flag;
    }

    private void destroyFlag(Player admin, Npc flag) {
        if (flag == null)
            return;

        final SM_DELETE pck = new SM_DELETE(flag, 19);

        admin.getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                PacketSendUtility.sendPacket(pl, pck);
                return true;
            }
        }, true);
    }
}
