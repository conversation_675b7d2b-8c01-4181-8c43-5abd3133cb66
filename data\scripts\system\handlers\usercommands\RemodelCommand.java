/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.dao.MightDAO;
import gameserver.dao.ShopDAO;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.ItemRemodelService;
import gameserver.services.PremiumService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.UserCommand;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class RemodelCommand extends UserCommand {
    private static final int REMODEL_PREVIEW_COST = 0;
    private static final int REMODEL_PREVIEW_DURATION = 30;

    public RemodelCommand() {
        super("remodel");
    }

    public void executeCommand(Player player, String param) {
        String[] params = Util.splitCommandArgs(param);

        if (params.length < 1 || params[0] == "") {
            PacketSendUtility.sendMessage(player,
                "Syntax: .remodel <itemid> -- remodels cost 1 [item: *********]"
                    + "\nSyntax: OR .remodel preview <itemid> -- previews last "
                    + REMODEL_PREVIEW_DURATION + " seconds.");
            return;
        }
        else if (params[0].equalsIgnoreCase("preview")) {
            if (DAOManager.getDAO(MightDAO.class).getMight(player) < REMODEL_PREVIEW_COST) {
                PacketSendUtility.sendMessage(player, "Remodel previews cost "
                    + REMODEL_PREVIEW_COST + " for " + REMODEL_PREVIEW_DURATION + " seconds!");
                return;
            }

            int itemId = 0;
            try {
                if (params[1].startsWith("[item: "))
                    itemId = Integer.parseInt(params[1].substring(7, 16));
                else if (params[1].startsWith("[item:"))
                    itemId = Integer.parseInt(params[1].substring(6, 15));
                else
                    itemId = Integer.parseInt(params[1]);
            }
            catch (Exception e) {
                PacketSendUtility.sendMessage(player,
                    "Error! Item id's are numbers like 100100715 or [item:100100715]!");
                return;
            }

            if (ItemRemodelService.commandPreviewRemodelItem(player, itemId,
                REMODEL_PREVIEW_DURATION)) {
                PacketSendUtility.sendMessage(player, "The remodel preview will stay for "
                    + REMODEL_PREVIEW_DURATION + " seconds.");
            }
            else {
                PacketSendUtility.sendMessage(player,
                    "It was not possible to remodel any of your equipped items to [item: " + itemId
                        + "]!");
            }
        }
        else {
            if (!PremiumService.isSuperPremium(player)
                && player.getInventory().getItemCountByItemId(*********) < 1) {
                PacketSendUtility.sendMessage(player,
                    "You need 1 [item: *********] for each item you wish to remodel!");
                return;
            }

            int itemId = 0;
            try {
                if (params[0].startsWith("[item: "))
                    itemId = Integer.parseInt(params[0].substring(7, 16));
                else if (params[0].startsWith("[item:"))
                    itemId = Integer.parseInt(params[0].substring(6, 15));
                else
                    itemId = Integer.parseInt(params[0]);
            }
            catch (Exception e) {
                PacketSendUtility.sendMessage(player,
                    "Error! Item id's are numbers like 100100715 or [item:100100715]!");
                return;
            }

            if (ItemRemodelService.commandRemodelItem(player, itemId)) {
                if (!PremiumService.isSuperPremium(player))
                    player.getInventory().removeFromBagByItemId(*********, 1);

                PacketSendUtility.sendMessage(player,
                    "Successfully remodeled one of your items to [item: " + itemId + "]!");

                DAOManager.getDAO(ShopDAO.class).logPurchase(player, -3, itemId, 1);
            }
            else {
                PacketSendUtility.sendMessage(player,
                    "It was not possible to remodel any of your equipped items to [item: " + itemId
                        + "]!");
            }
        }
    }
}
