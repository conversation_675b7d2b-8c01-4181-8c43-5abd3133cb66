/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.configs.main.CustomConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.util.Map;

import javolution.util.FastMap;

/**
 * 
 * <AUTHOR>
 */
public class GloryCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new FastMap<Integer, Long>();

    public GloryCommand() {
        super("glory");
    }

    public void executeCommand(Player player, String param) {
        if (CustomConfig.OLD_SCHOOL)
            return;
        
        String[] params = param.split(" ");

        if (player.isInCombatLong()) {
            PacketSendUtility.sendMessage(player, "You cannot use this command while in combat!");
            return;
        }

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 1000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 1 seconds!");
                return;
            }
        }

        int value = 100;

        if (player.getAbyssRank().getGlory() - value < 0)
            value = player.getAbyssRank().getGlory();

        player.getCommonData().addGlory(-value);

        PacketSendUtility
            .sendMessage(player, "You have removed " + value + " Glory from yourself!");

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }
}