/*
 Navicat Premium Dump SQL

 Source Server         : not-aion
 Source Server Type    : MariaDB
 Source Server Version : 100414 (10.4.14-MariaDB)
 Source Host           : localhost:3306
 Source Schema         : not-aion

 Target Server Type    : MariaDB
 Target Server Version : 100414 (10.4.14-MariaDB)
 File Encoding         : 65001

 Date: 22/07/2025 02:13:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for abyss_rank
-- ----------------------------
DROP TABLE IF EXISTS `abyss_rank`;
CREATE TABLE `abyss_rank`  (
  `player_id` int(11) NOT NULL,
  `daily_ap` int(11) NOT NULL,
  `weekly_ap` int(11) NOT NULL,
  `ap` int(11) NOT NULL,
  `daily_glory` int(11) NOT NULL DEFAULT 0,
  `weekly_glory` int(11) NOT NULL DEFAULT 0,
  `glory` int(11) NOT NULL DEFAULT 0,
  `gp` int(11) NOT NULL DEFAULT 0,
  `rank` int(2) NOT NULL DEFAULT 1,
  `top_ranking` int(5) NOT NULL DEFAULT 0,
  `old_ranking` int(5) NOT NULL DEFAULT 0,
  `daily_kill` int(5) NOT NULL,
  `weekly_kill` int(5) NOT NULL,
  `all_kill` int(4) NOT NULL DEFAULT 0,
  `max_rank` int(2) NOT NULL DEFAULT 1,
  `last_kill` int(5) NOT NULL,
  `last_ap` int(11) NOT NULL,
  `last_glory` int(11) NOT NULL DEFAULT 0,
  `last_update` decimal(20, 0) NOT NULL,
  PRIMARY KEY (`player_id`) USING BTREE,
  INDEX `idx_abyss_rank_player_id`(`player_id`) USING BTREE,
  CONSTRAINT `abyss_rank_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of abyss_rank
-- ----------------------------
INSERT INTO `abyss_rank` VALUES (1221, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, *************);
INSERT INTO `abyss_rank` VALUES (1232, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, *************);
INSERT INTO `abyss_rank` VALUES (1300, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, *************);

-- ----------------------------
-- Table structure for access_log
-- ----------------------------
DROP TABLE IF EXISTS `access_log`;
CREATE TABLE `access_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_name` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `date` timestamp NOT NULL DEFAULT current_timestamp(),
  `response` int(11) NOT NULL,
  `ip` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ip`(`ip`) USING BTREE,
  INDEX `date`(`date`) USING BTREE,
  INDEX `response`(`response`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of access_log
-- ----------------------------
INSERT INTO `access_log` VALUES (1, 'test22', '2025-07-21 03:45:50', 0, '*************');
INSERT INTO `access_log` VALUES (2, 'test22', '2025-07-21 03:47:55', 0, '*************');
INSERT INTO `access_log` VALUES (3, 'test22', '2025-07-21 04:02:57', 0, '*************');
INSERT INTO `access_log` VALUES (4, 'test22', '2025-07-21 04:08:26', 0, '*************');
INSERT INTO `access_log` VALUES (5, 'test22', '2025-07-21 04:08:38', 0, '*************');
INSERT INTO `access_log` VALUES (6, 'test22', '2025-07-21 04:09:38', 0, '*************');
INSERT INTO `access_log` VALUES (7, 'test22', '2025-07-21 04:13:43', 0, '*************');
INSERT INTO `access_log` VALUES (8, 'test22', '2025-07-21 04:18:40', 0, '*************');
INSERT INTO `access_log` VALUES (9, 'test22', '2025-07-21 04:20:10', 0, '*************');
INSERT INTO `access_log` VALUES (10, 'loco1010', '2025-07-21 04:57:57', 0, '38.25.51.153');
INSERT INTO `access_log` VALUES (11, 'test22', '2025-07-21 05:12:49', 0, '*************');
INSERT INTO `access_log` VALUES (12, 'test22', '2025-07-21 05:23:46', 0, '*************');
INSERT INTO `access_log` VALUES (13, 'test22', '2025-07-21 05:30:15', 0, '*************');
INSERT INTO `access_log` VALUES (14, 'test22', '2025-07-21 05:32:52', 0, '*************');
INSERT INTO `access_log` VALUES (15, 'test22', '2025-07-21 05:39:01', 0, '*************');
INSERT INTO `access_log` VALUES (16, 'test22', '2025-07-21 05:49:24', 0, '*************');
INSERT INTO `access_log` VALUES (17, 'test22', '2025-07-21 22:25:07', 0, '*************');
INSERT INTO `access_log` VALUES (18, 'test22', '2025-07-21 22:28:12', 0, '*************');
INSERT INTO `access_log` VALUES (19, 'test22', '2025-07-22 00:12:13', 0, '*************');
INSERT INTO `access_log` VALUES (20, 'test22', '2025-07-22 02:04:00', 0, '*************');

-- ----------------------------
-- Table structure for account_balance
-- ----------------------------
DROP TABLE IF EXISTS `account_balance`;
CREATE TABLE `account_balance`  (
  `account_id` int(11) NOT NULL,
  `price_id` int(11) NOT NULL,
  `value` bigint(20) NOT NULL DEFAULT 0,
  PRIMARY KEY (`account_id`, `price_id`) USING BTREE,
  INDEX `price_id`(`price_id`) USING BTREE,
  CONSTRAINT `account_balance_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `account_balance_ibfk_2` FOREIGN KEY (`price_id`) REFERENCES `price_type` (`price_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_balance
-- ----------------------------

-- ----------------------------
-- Table structure for account_data
-- ----------------------------
DROP TABLE IF EXISTS `account_data`;
CREATE TABLE `account_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `password` varchar(65) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `activated` tinyint(1) NOT NULL DEFAULT 1,
  `access_level` tinyint(3) NOT NULL DEFAULT 0,
  `membership` tinyint(3) NOT NULL DEFAULT 0,
  `last_server` tinyint(3) NOT NULL DEFAULT -1,
  `last_ip` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ip_force` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `expire` date NULL DEFAULT NULL,
  `pin` int(11) NULL DEFAULT NULL,
  `creation_date` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE,
  INDEX `idx_account_data_name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_data
-- ----------------------------
INSERT INTO `account_data` VALUES (1, 'admin', 'admin', 1, 3, 0, -1, NULL, NULL, NULL, NULL, '2025-07-21 04:32:23');
INSERT INTO `account_data` VALUES (2, 'test', 'test', 1, 0, 0, -1, NULL, NULL, NULL, NULL, '2025-07-21 04:32:23');
INSERT INTO `account_data` VALUES (3, 'test22', 'jlmgi6QB2ort2VizplwtjnDcjaI=', 1, 6, 0, 1, '*************', NULL, NULL, NULL, '2025-07-21 04:32:23');
INSERT INTO `account_data` VALUES (4, 'loco1010', '7GpLVpttNVAASim8cxcEHgxEcBA=', 1, 6, 0, 0, '38.25.51.153', NULL, NULL, NULL, '2025-07-21 04:57:57');
INSERT INTO `account_data` VALUES (5, 'thuong', 'NSUXjJPTjGSKd2Ndy1BBnm8D1OM=', 1, 0, 0, -1, NULL, NULL, NULL, NULL, '2025-07-21 23:18:51');
INSERT INTO `account_data` VALUES (6, 'Kakura', '744iU0EJ/Eu1Au5apsmBN76zrmk=', 1, 0, 0, -1, NULL, NULL, NULL, NULL, '2025-07-22 01:43:38');

-- ----------------------------
-- Table structure for account_stamps
-- ----------------------------
DROP TABLE IF EXISTS `account_stamps`;
CREATE TABLE `account_stamps`  (
  `account_id` int(11) NOT NULL,
  `stamps` int(11) NOT NULL DEFAULT 0,
  `last_stamp` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`account_id`) USING BTREE,
  CONSTRAINT `account_stamps_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_stamps
-- ----------------------------

-- ----------------------------
-- Table structure for account_time
-- ----------------------------
DROP TABLE IF EXISTS `account_time`;
CREATE TABLE `account_time`  (
  `account_id` int(11) NOT NULL,
  `last_active` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP,
  `expiration_time` timestamp NULL DEFAULT NULL,
  `session_duration` int(10) NULL DEFAULT 0,
  `accumulated_online` int(10) NULL DEFAULT 0,
  `accumulated_rest` int(10) NULL DEFAULT 0,
  `penalty_end` timestamp NULL DEFAULT NULL,
  `penalty_reason` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`account_id`) USING BTREE,
  CONSTRAINT `account_time_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_time
-- ----------------------------
INSERT INTO `account_time` VALUES (3, '2025-07-22 02:04:00', NULL, 6202002, 0, 0, NULL, NULL);
INSERT INTO `account_time` VALUES (4, '2025-07-21 04:57:57', NULL, 0, 0, 0, NULL, NULL);

-- ----------------------------
-- Table structure for announcements
-- ----------------------------
DROP TABLE IF EXISTS `announcements`;
CREATE TABLE `announcements`  (
  `id` int(3) NOT NULL AUTO_INCREMENT,
  `announce` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `faction` enum('ALL','ASMODIANS','ELYOS') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'ALL',
  `type` enum('ANNOUNCE','SHOUT','ORANGE','YELLOW','NORMAL') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'ANNOUNCE',
  `delay` int(4) NOT NULL DEFAULT 1800,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of announcements
-- ----------------------------

-- ----------------------------
-- Table structure for balance_history
-- ----------------------------
DROP TABLE IF EXISTS `balance_history`;
CREATE TABLE `balance_history`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` datetime NOT NULL,
  `reference_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `account_id` int(11) NOT NULL,
  `account_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `price_id` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` enum('IN','OUT') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 45 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of balance_history
-- ----------------------------
INSERT INTO `balance_history` VALUES (7, '2025-07-21 05:06:45', 'SHOP_3_1753067205', 3, '0', 1, 5, 'Purchased: Kinah Bundle (1000) (x1)', 'OUT');
INSERT INTO `balance_history` VALUES (8, '2025-07-21 05:07:00', 'SHOP_3_1753067220', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10)', 'OUT');
INSERT INTO `balance_history` VALUES (9, '2025-07-21 05:19:29', 'SHOP_3_1753067969', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10)', 'OUT');
INSERT INTO `balance_history` VALUES (10, '2025-07-21 05:23:58', 'SHOP_3_1753068238', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10)', 'OUT');
INSERT INTO `balance_history` VALUES (11, '2025-07-21 05:24:04', 'SHOP_3_1753068244', 3, '0', 1, 5, 'Purchased: Kinah Bundle (1000) (x1)', 'OUT');
INSERT INTO `balance_history` VALUES (12, '2025-07-21 05:24:08', 'SHOP_3_1753068248', 3, '0', 1, 10, 'Purchased: Test Reward Item (x5)', 'OUT');
INSERT INTO `balance_history` VALUES (13, '2025-07-21 05:26:29', 'SHOP_3_1753068389', 3, '0', 1, 5, 'Purchased: Kinah Bundle (1000) (x1)', 'OUT');
INSERT INTO `balance_history` VALUES (14, '2025-07-21 05:29:44', 'SHOP_3_1753068584', 3, '0', 1, 5, 'Purchased: Kinah Bundle (1000) (x1)', 'OUT');
INSERT INTO `balance_history` VALUES (15, '2025-07-21 05:33:09', 'SHOP_3_1753068789', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10)', 'OUT');
INSERT INTO `balance_history` VALUES (16, '2025-07-21 05:33:13', 'SHOP_3_1753068793', 3, '0', 1, 5, 'Purchased: Kinah Bundle (1000) (x1)', 'OUT');
INSERT INTO `balance_history` VALUES (17, '2025-07-21 05:47:45', 'SHOP_3_1753069665', 3, '0', 1, 5, 'Purchased: Kinah Bundle (1000) (x1)', 'OUT');
INSERT INTO `balance_history` VALUES (18, '2025-07-21 05:51:07', 'SHOP_3_1753069867', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10)', 'OUT');
INSERT INTO `balance_history` VALUES (19, '2025-07-21 06:00:38', 'SHOP_3_1753070438', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10)', 'OUT');
INSERT INTO `balance_history` VALUES (20, '2025-07-21 06:01:23', 'SHOP_3_1753070483', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10)', 'OUT');
INSERT INTO `balance_history` VALUES (21, '2025-07-21 06:01:26', 'SHOP_3_1753070486', 3, '0', 1, 5, 'Purchased: Kinah Bundle (1000) (x1)', 'OUT');
INSERT INTO `balance_history` VALUES (22, '2025-07-21 06:02:33', 'SHOP_3_1753070553', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10)', 'OUT');
INSERT INTO `balance_history` VALUES (23, '2025-07-21 06:04:00', 'SHOP_3_1753070640', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10)', 'OUT');
INSERT INTO `balance_history` VALUES (24, '2025-07-21 06:04:07', 'SHOP_3_1753070647', 3, '0', 1, 10, 'Purchased: Test Reward Item (x5)', 'OUT');
INSERT INTO `balance_history` VALUES (25, '2025-07-22 00:09:56', 'SHOP_3_1753135796', 3, '0', 1, 10, 'Purchased: Test Reward Item (x5)', 'OUT');
INSERT INTO `balance_history` VALUES (26, '2025-07-22 00:10:06', 'SHOP_3_1753135806', 3, '0', 1, 10, 'Purchased: Test Reward Item (x5)', 'OUT');
INSERT INTO `balance_history` VALUES (27, '2025-07-22 00:12:37', 'SHOP_3_1753135957', 3, '0', 1, 5, 'Purchased: Kinah Bundle (1000) (x1)', 'OUT');
INSERT INTO `balance_history` VALUES (28, '2025-07-22 00:54:29', 'SHOP_3_1753138469', 3, '0', 1, 5, 'Purchased: Kinah Bundle (1000) (x1) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (29, '2025-07-22 00:54:44', 'SHOP_3_1753138484', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (30, '2025-07-22 02:04:40', 'SHOP_3_1753142680', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (31, '2025-07-22 02:04:44', 'SHOP_3_1753142684', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (32, '2025-07-22 02:05:02', 'SHOP_3_1753142702', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (33, '2025-07-22 02:05:05', 'SHOP_3_1753142705', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (34, '2025-07-22 02:05:13', 'SHOP_3_1753142713', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (35, '2025-07-22 02:05:17', 'SHOP_3_1753142717', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (36, '2025-07-22 02:05:20', 'SHOP_3_1753142720', 3, '0', 1, 10, 'Purchased: Test Reward Item (x5) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (37, '2025-07-22 02:05:29', 'SHOP_3_1753142729', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (38, '2025-07-22 02:05:32', 'SHOP_3_1753142732', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (39, '2025-07-22 02:05:36', 'SHOP_3_1753142736', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (40, '2025-07-22 02:05:39', 'SHOP_3_1753142739', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (41, '2025-07-22 02:05:42', 'SHOP_3_1753142742', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (42, '2025-07-22 02:05:45', 'SHOP_3_1753142745', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (43, '2025-07-22 02:05:48', 'SHOP_3_1753142748', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');
INSERT INTO `balance_history` VALUES (44, '2025-07-22 02:05:51', 'SHOP_3_1753142751', 3, '0', 1, 15, 'Purchased: Premium Health Potion (x10) with Might Points', 'OUT');

-- ----------------------------
-- Table structure for banned_ip
-- ----------------------------
DROP TABLE IF EXISTS `banned_ip`;
CREATE TABLE `banned_ip`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mask` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time_end` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `mask`(`mask`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of banned_ip
-- ----------------------------

-- ----------------------------
-- Table structure for banned_mac
-- ----------------------------
DROP TABLE IF EXISTS `banned_mac`;
CREATE TABLE `banned_mac`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `address` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `details` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `time_end` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `address`(`address`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of banned_mac
-- ----------------------------

-- ----------------------------
-- Table structure for betsystem_conditions
-- ----------------------------
DROP TABLE IF EXISTS `betsystem_conditions`;
CREATE TABLE `betsystem_conditions`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `conditionID` int(11) NOT NULL,
  `eventID` int(11) NOT NULL,
  `expectedValue` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of betsystem_conditions
-- ----------------------------

-- ----------------------------
-- Table structure for betsystem_events
-- ----------------------------
DROP TABLE IF EXISTS `betsystem_events`;
CREATE TABLE `betsystem_events`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `value` int(11) NULL DEFAULT -1,
  `name` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `groupname` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'GLOBAL',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of betsystem_events
-- ----------------------------

-- ----------------------------
-- Table structure for blocks
-- ----------------------------
DROP TABLE IF EXISTS `blocks`;
CREATE TABLE `blocks`  (
  `player` int(11) NOT NULL,
  `blocked_player` int(11) NOT NULL,
  `reason` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`player`, `blocked_player`) USING BTREE,
  INDEX `blocked_player`(`blocked_player`) USING BTREE,
  CONSTRAINT `blocks_ibfk_1` FOREIGN KEY (`player`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `blocks_ibfk_2` FOREIGN KEY (`blocked_player`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of blocks
-- ----------------------------

-- ----------------------------
-- Table structure for bookmark
-- ----------------------------
DROP TABLE IF EXISTS `bookmark`;
CREATE TABLE `bookmark`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `char_id` int(11) NOT NULL,
  `x` float NOT NULL,
  `y` float NOT NULL,
  `z` float NOT NULL,
  `world_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bookmark
-- ----------------------------

-- ----------------------------
-- Table structure for broker
-- ----------------------------
DROP TABLE IF EXISTS `broker`;
CREATE TABLE `broker`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `itemPointer` int(11) NOT NULL DEFAULT 0,
  `itemId` int(11) NOT NULL,
  `itemCount` bigint(20) NOT NULL,
  `seller` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `price` bigint(20) NOT NULL DEFAULT 0,
  `brokerRace` enum('ELYOS','ASMODIAN') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `expireTime` timestamp NOT NULL DEFAULT '2010-01-01 03:00:00',
  `settleTime` timestamp NOT NULL DEFAULT '2010-01-01 03:00:00',
  `sellerId` int(11) NOT NULL,
  `isSold` tinyint(1) NOT NULL,
  `isSettled` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of broker
-- ----------------------------

-- ----------------------------
-- Table structure for donations
-- ----------------------------
DROP TABLE IF EXISTS `donations`;
CREATE TABLE `donations`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` decimal(10, 2) NOT NULL,
  `paypal_order_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `status` enum('pending','completed','failed','cancelled') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  CONSTRAINT `donations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of donations
-- ----------------------------

-- ----------------------------
-- Table structure for droplist
-- ----------------------------
DROP TABLE IF EXISTS `droplist`;
CREATE TABLE `droplist`  (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `mobId` int(11) NOT NULL DEFAULT 0,
  `itemId` int(11) NOT NULL DEFAULT 0,
  `min` int(11) NOT NULL DEFAULT 0,
  `max` int(11) NOT NULL DEFAULT 0,
  `chance` float NOT NULL DEFAULT 0,
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of droplist
-- ----------------------------

-- ----------------------------
-- Table structure for friends
-- ----------------------------
DROP TABLE IF EXISTS `friends`;
CREATE TABLE `friends`  (
  `player` int(11) NOT NULL,
  `friend` int(11) NOT NULL,
  PRIMARY KEY (`player`, `friend`) USING BTREE,
  INDEX `friend`(`friend`) USING BTREE,
  CONSTRAINT `friends_ibfk_1` FOREIGN KEY (`player`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `friends_ibfk_2` FOREIGN KEY (`friend`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of friends
-- ----------------------------

-- ----------------------------
-- Table structure for gameservers
-- ----------------------------
DROP TABLE IF EXISTS `gameservers`;
CREATE TABLE `gameservers`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mask` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `password` varchar(65) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gameservers
-- ----------------------------
INSERT INTO `gameservers` VALUES (1, '*', 'Azer$tyuio^p789');

-- ----------------------------
-- Table structure for instance_scores
-- ----------------------------
DROP TABLE IF EXISTS `instance_scores`;
CREATE TABLE `instance_scores`  (
  `player_id` int(11) NOT NULL,
  `instance_id` int(11) NOT NULL,
  `score` int(11) NOT NULL DEFAULT 0,
  `objectives` int(11) NOT NULL DEFAULT 0,
  `seconds_left` int(11) NOT NULL DEFAULT 0,
  `extra` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`, `instance_id`) USING BTREE,
  INDEX `idx_player_id`(`player_id`) USING BTREE,
  INDEX `idx_instance_id`(`instance_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of instance_scores
-- ----------------------------

-- ----------------------------
-- Table structure for instance_time
-- ----------------------------
DROP TABLE IF EXISTS `instance_time`;
CREATE TABLE `instance_time`  (
  `playerId` int(11) NULL DEFAULT NULL,
  `instanceId` int(11) NULL DEFAULT NULL,
  `CheckIn` bigint(20) NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of instance_time
-- ----------------------------

-- ----------------------------
-- Table structure for inventory
-- ----------------------------
DROP TABLE IF EXISTS `inventory`;
CREATE TABLE `inventory`  (
  `itemUniqueId` int(11) NOT NULL,
  `itemId` int(11) NOT NULL,
  `itemCount` bigint(20) NOT NULL DEFAULT 0,
  `itemColor` int(11) NOT NULL DEFAULT 0,
  `itemOwner` int(11) NOT NULL,
  `isEquiped` tinyint(1) NOT NULL DEFAULT 0,
  `isSoulBound` tinyint(1) NOT NULL DEFAULT 0,
  `slot` int(11) NOT NULL DEFAULT 0,
  `itemLocation` tinyint(1) NULL DEFAULT 0,
  `enchant` tinyint(1) NULL DEFAULT 0,
  `itemCreator` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `itemSkin` int(11) NOT NULL DEFAULT 0,
  `fusionedItem` int(11) NOT NULL DEFAULT 0,
  `optionalSocket` int(1) NOT NULL DEFAULT 0,
  `optionalFusionSocket` int(1) NOT NULL DEFAULT 0,
  `conditioning` int(11) NOT NULL DEFAULT 0,
  `temperance` int(11) NOT NULL DEFAULT 0,
  `expireTime` timestamp NULL DEFAULT NULL,
  `randomOption` int(11) NOT NULL DEFAULT 0,
  `bonusEnchant` int(11) NOT NULL DEFAULT 0,
  `randomFusionOption` int(11) NOT NULL DEFAULT 0,
  `wrapped` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`itemUniqueId`) USING BTREE,
  INDEX `item_owner`(`itemOwner`) USING BTREE,
  INDEX `item_location`(`itemLocation`) USING BTREE,
  INDEX `is_equiped`(`isEquiped`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of inventory
-- ----------------------------
INSERT INTO `inventory` VALUES (1, 186000236, 1, 0, 1217, 0, 0, 0, 127, 0, '', 0, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (2, 186000242, 10, 0, 1217, 0, 0, 0, 127, 0, '', 0, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (3, 100000001, 10, 0, 1217, 0, 0, 0, 127, 0, '', 0, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (4, 100000001, 10, 0, 1217, 0, 0, 0, 127, 0, '', 0, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (5, 186000236, 1, 0, 1217, 0, 0, 0, 127, 0, '', 0, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (6, 164000073, 5, 0, 1217, 0, 0, 0, 127, 0, '', 0, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (7, 186000236, 1, 0, 1217, 0, 0, 0, 127, 0, '', 0, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (8, 186000236, 1, 0, 1217, 0, 0, 0, 127, 0, '', 0, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (9, 100000001, 10, 0, 1217, 0, 0, 0, 127, 0, '', 0, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (10, 186000236, 1, 0, 1217, 0, 0, 0, 127, 0, '', 0, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (12, 141000001, 9000, 0, 1232, 0, 0, 0, 0, 0, NULL, 141000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (13, 182400001, 0, 0, 1300, 0, 0, 65535, 0, 0, NULL, 182400001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (14, 141000001, 9000, 0, 1221, 0, 0, 0, 0, 0, NULL, 141000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (15, 101701263, 1, 0, 1221, 0, 0, 1, 0, 10, NULL, 101701263, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (16, 120001480, 1, 0, 1221, 0, 0, 2, 0, 0, NULL, 120001480, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (17, 120001480, 1, 0, 1221, 0, 0, 3, 0, 0, NULL, 120001480, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (18, 121001354, 1, 0, 1221, 0, 0, 4, 0, 0, NULL, 121001354, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (19, 122001623, 1, 0, 1221, 0, 0, 5, 0, 0, NULL, 122001623, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (20, 122001623, 1, 0, 1221, 0, 0, 6, 0, 0, NULL, 122001623, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (21, 125003352, 1, 0, 1221, 0, 0, 7, 0, 0, NULL, 125003352, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (22, 123001403, 1, 0, 1221, 0, 0, 8, 0, 0, NULL, 123001403, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (23, 141000001, 9000, 0, 1300, 0, 0, 0, 0, 0, NULL, 141000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (24, 182400001, 0, 0, 1221, 0, 0, 65535, 0, 0, NULL, 182400001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (25, 101701263, 1, 0, 1300, 1, 0, 1, 0, 10, NULL, 101701263, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (26, 120001480, 1, 0, 1300, 1, 0, 64, 0, 0, NULL, 120001480, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (27, 120001480, 1, 0, 1300, 1, 0, 128, 0, 0, NULL, 120001480, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (28, 121001354, 1, 0, 1300, 1, 0, 1024, 0, 0, NULL, 121001354, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (29, 122001623, 1, 0, 1300, 1, 0, 256, 0, 0, NULL, 122001623, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (30, 122001623, 1, 0, 1300, 1, 0, 512, 0, 0, NULL, 122001623, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (31, 125003352, 1, 0, 1300, 1, 0, 4, 0, 0, NULL, 125003352, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (32, 123001403, 1, 0, 1300, 1, 0, 65536, 0, 0, NULL, 123001403, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (33, 100601304, 1, 0, 1232, 0, 0, 2, 0, 10, NULL, 100601304, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (34, 100501212, 1, 0, 1232, 1, 0, 1, 0, 10, NULL, 100501212, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (35, 120001481, 1, 0, 1232, 1, 0, 64, 0, 0, NULL, 120001481, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (36, 120001481, 1, 0, 1232, 1, 0, 128, 0, 0, NULL, 120001481, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (37, 121001355, 1, 0, 1232, 1, 0, 1024, 0, 0, NULL, 121001355, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (38, 122001624, 1, 0, 1232, 1, 0, 256, 0, 0, NULL, 122001624, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (39, 122001624, 1, 0, 1232, 1, 0, 512, 0, 0, NULL, 122001624, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (40, 125003353, 1, 0, 1232, 1, 0, 4, 0, 0, NULL, 125003353, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (41, 123001404, 1, 0, 1232, 1, 0, 65536, 0, 0, NULL, 123001404, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (42, 100000001, 1, 0, 1221, 0, 0, 27, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (43, 100000001, 1, 0, 1221, 0, 0, 28, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (44, 100000001, 1, 0, 1221, 0, 0, 29, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (45, 186000236, 1, 0, 1221, 0, 0, 0, 0, 0, 'Afaf', 186000236, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (46, 100000001, 1, 0, 1221, 0, 0, 30, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (47, 100000001, 1, 0, 1221, 0, 0, 31, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (48, 100000001, 1, 0, 1221, 0, 0, 32, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (49, 100000001, 1, 0, 1221, 0, 0, 33, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (50, 100000001, 1, 0, 1221, 0, 0, 34, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (51, 100000001, 1, 0, 1221, 0, 0, 35, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (52, 100000001, 1, 0, 1221, 0, 0, 36, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (53, 100000001, 1, 0, 1221, 0, 0, 37, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (54, 100000001, 1, 0, 1221, 0, 0, 38, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (55, 100000001, 1, 0, 1221, 0, 0, 39, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (56, 100000001, 1, 0, 1221, 0, 0, 9, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (57, 100000001, 1, 0, 1221, 0, 0, 10, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (58, 100000001, 1, 0, 1221, 0, 0, 11, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (59, 100000001, 1, 0, 1221, 0, 0, 12, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (60, 100000001, 1, 0, 1221, 0, 0, 13, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (61, 100000001, 1, 0, 1221, 0, 0, 14, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (62, 100000001, 1, 0, 1221, 0, 0, 15, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (63, 100000001, 1, 0, 1221, 0, 0, 16, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (64, 100000001, 1, 0, 1221, 0, 0, 17, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (65, 100000001, 1, 0, 1221, 0, 0, 18, 0, 0, 'Afaf', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (66, 164000073, 4, 0, 1221, 0, 0, 19, 0, 0, 'Afaf', 164000073, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (67, 182400001, 0, 0, 1232, 0, 0, 65535, 0, 0, NULL, 182400001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (68, 164000073, 15, 0, 1232, 0, 0, 1, 0, 0, 'Tata', 164000073, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (69, 100000001, 1, 0, 1232, 0, 0, 13, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (70, 186000236, 2, 0, 1232, 0, 0, 0, 0, 0, 'Tata', 186000236, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (71, 100000001, 1, 0, 1232, 0, 0, 3, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (72, 100000001, 1, 0, 1232, 0, 0, 4, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (73, 100000001, 1, 0, 1232, 0, 0, 5, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (74, 100000001, 1, 0, 1232, 0, 0, 6, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (75, 100000001, 1, 0, 1232, 0, 0, 7, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (76, 100000001, 1, 0, 1232, 0, 0, 8, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (77, 100000001, 1, 0, 1232, 0, 0, 9, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (78, 100000001, 1, 0, 1232, 0, 0, 10, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (79, 100000001, 1, 0, 1232, 0, 0, 11, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (80, 100000001, 1, 0, 1232, 0, 0, 12, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (81, 100000001, 1, 0, 1232, 0, 0, 14, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (82, 100000001, 1, 0, 1232, 0, 0, 15, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (83, 100000001, 1, 0, 1232, 0, 0, 16, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (84, 100000001, 1, 0, 1232, 0, 0, 17, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (85, 100000001, 1, 0, 1232, 0, 0, 18, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (86, 100000001, 1, 0, 1232, 0, 0, 19, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (87, 100000001, 1, 0, 1232, 0, 0, 20, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (88, 100000001, 1, 0, 1232, 0, 0, 21, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (89, 100000001, 1, 0, 1232, 0, 0, 22, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (90, 100000001, 1, 0, 1232, 0, 0, 23, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (91, 100000001, 1, 0, 1232, 0, 0, 24, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (92, 100000001, 1, 0, 1232, 0, 0, 25, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (93, 100000001, 1, 0, 1232, 0, 0, 26, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (94, 100000001, 1, 0, 1232, 0, 0, 27, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (95, 100000001, 1, 0, 1232, 0, 0, 28, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (96, 100000001, 1, 0, 1232, 0, 0, 29, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (97, 100000001, 1, 0, 1232, 0, 0, 30, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (98, 100000001, 1, 0, 1232, 0, 0, 31, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (99, 100000001, 1, 0, 1232, 0, 0, 32, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (100, 100000001, 1, 0, 1232, 0, 0, 33, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (101, 100000001, 1, 0, 1232, 0, 0, 34, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (102, 100000001, 1, 0, 1232, 0, 0, 35, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (103, 100000001, 1, 0, 1232, 0, 0, 36, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (104, 100000001, 1, 0, 1232, 0, 0, 37, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105, 100000001, 1, 0, 1232, 0, 0, 38, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106, 100000001, 1, 0, 1232, 0, 0, 39, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (107, 100000001, 1, 0, 1232, 0, 0, 40, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (108, 100000001, 1, 0, 1232, 0, 0, 41, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (109, 100000001, 1, 0, 1232, 0, 0, 42, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (110, 100000001, 1, 0, 1232, 0, 0, 43, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (111, 100000001, 1, 0, 1232, 0, 0, 44, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (112, 100000001, 1, 0, 1232, 0, 0, 45, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (113, 100000001, 1, 0, 1232, 0, 0, 46, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (114, 100000001, 1, 0, 1232, 0, 0, 47, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (115, 100000001, 1, 0, 1232, 0, 0, 48, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (116, 100000001, 1, 0, 1232, 0, 0, 49, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (117, 100000001, 1, 0, 1232, 0, 0, 50, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (118, 100000001, 1, 0, 1232, 0, 0, 51, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (119, 100000001, 1, 0, 1232, 0, 0, 52, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (120, 100000001, 1, 0, 1232, 0, 0, 53, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (121, 100000001, 1, 0, 1232, 0, 0, 54, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (122, 100000001, 1, 0, 1232, 0, 0, 55, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (123, 100000001, 1, 0, 1232, 0, 0, 56, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (124, 100000001, 1, 0, 1232, 0, 0, 57, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (125, 100000001, 1, 0, 1232, 0, 0, 58, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (126, 100000001, 1, 0, 1232, 0, 0, 59, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (127, 100000001, 1, 0, 1232, 0, 0, 60, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (128, 100000001, 1, 0, 1232, 0, 0, 61, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (129, 100000001, 1, 0, 1232, 0, 0, 62, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (130, 100000001, 1, 0, 1232, 0, 0, 63, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (131, 100000001, 1, 0, 1232, 0, 0, 64, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (132, 100000001, 1, 0, 1232, 0, 0, 65, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (133, 100000001, 1, 0, 1232, 0, 0, 66, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (134, 100000001, 1, 0, 1232, 0, 0, 67, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (135, 100000001, 1, 0, 1232, 0, 0, 68, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (136, 100000001, 1, 0, 1232, 0, 0, 69, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (137, 100000001, 1, 0, 1232, 0, 0, 70, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (138, 100000001, 1, 0, 1232, 0, 0, 71, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (139, 100000001, 1, 0, 1232, 0, 0, 72, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (140, 100000001, 1, 0, 1232, 0, 0, 73, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (141, 100000001, 1, 0, 1232, 0, 0, 74, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (142, 100000001, 1, 0, 1232, 0, 0, 75, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (143, 100000001, 1, 0, 1232, 0, 0, 76, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (144, 100000001, 1, 0, 1232, 0, 0, 77, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (145, 100000001, 1, 0, 1232, 0, 0, 78, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (146, 100000001, 1, 0, 1232, 0, 0, 79, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (147, 100000001, 1, 0, 1232, 0, 0, 80, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (148, 100000001, 1, 0, 1232, 0, 0, 81, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (149, 100000001, 1, 0, 1232, 0, 0, 82, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (150, 100000001, 1, 0, 1232, 0, 0, 83, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (151, 100000001, 1, 0, 1232, 0, 0, 84, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (152, 100000001, 1, 0, 1232, 0, 0, 85, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (153, 100000001, 1, 0, 1232, 0, 0, 86, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (154, 100000001, 1, 0, 1232, 0, 0, 87, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (155, 100000001, 1, 0, 1232, 0, 0, 88, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (156, 100000001, 1, 0, 1232, 0, 0, 89, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (157, 100000001, 1, 0, 1232, 0, 0, 90, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (158, 100000001, 1, 0, 1232, 0, 0, 91, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (159, 100000001, 1, 0, 1232, 0, 0, 92, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (160, 100000001, 1, 0, 1232, 0, 0, 93, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (161, 100000001, 1, 0, 1232, 0, 0, 94, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (162, 100000001, 1, 0, 1232, 0, 0, 95, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (163, 100000001, 1, 0, 1232, 0, 0, 96, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (164, 100000001, 1, 0, 1232, 0, 0, 97, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (165, 100000001, 1, 0, 1232, 0, 0, 98, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (166, 100000001, 1, 0, 1232, 0, 0, 99, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (167, 100000001, 1, 0, 1232, 0, 0, 100, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (168, 100000001, 1, 0, 1232, 0, 0, 101, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (169, 100000001, 1, 0, 1232, 0, 0, 102, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (170, 100000001, 1, 0, 1232, 0, 0, 103, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (171, 100000001, 1, 0, 1232, 0, 0, 104, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (172, 100000001, 1, 0, 1232, 0, 0, 105, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (173, 100000001, 1, 0, 1232, 0, 0, 106, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (174, 100000001, 1, 0, 1232, 0, 0, 107, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (175, 100000001, 1, 0, 1232, 0, 0, 108, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (176, 100000001, 1, 0, 1232, 0, 0, 109, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (177, 100000001, 1, 0, 1232, 0, 0, 110, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (178, 100000001, 1, 0, 1232, 0, 0, 111, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (179, 100000001, 1, 0, 1232, 0, 0, 112, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (180, 100000001, 1, 0, 1232, 0, 0, 113, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (181, 100000001, 1, 0, 1232, 0, 0, 114, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (182, 100000001, 1, 0, 1232, 0, 0, 115, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (183, 100000001, 1, 0, 1232, 0, 0, 116, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (184, 100000001, 1, 0, 1232, 0, 0, 117, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (185, 100000001, 1, 0, 1232, 0, 0, 118, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (186, 100000001, 1, 0, 1232, 0, 0, 119, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (187, 100000001, 1, 0, 1232, 0, 0, 120, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (188, 100000001, 1, 0, 1232, 0, 0, 121, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (189, 100000001, 1, 0, 1232, 0, 0, 122, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (190, 100000001, 1, 0, 1232, 0, 0, 123, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (191, 100000001, 1, 0, 1232, 0, 0, 124, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (192, 100000001, 1, 0, 1232, 0, 0, 125, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (193, 100000001, 1, 0, 1232, 0, 0, 126, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (194, 100000001, 1, 0, 1232, 0, 0, 127, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (195, 100000001, 1, 0, 1232, 0, 0, 128, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (196, 100000001, 1, 0, 1232, 0, 0, 129, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (197, 100000001, 1, 0, 1232, 0, 0, 130, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (198, 100000001, 1, 0, 1232, 0, 0, 131, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (199, 100000001, 1, 0, 1232, 0, 0, 132, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (200, 141000001, 9000, 0, 1224, 0, 0, 65535, 0, 0, NULL, 141000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (201, 100601304, 1, 0, 1224, 0, 0, 65535, 0, 10, NULL, 100601304, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (202, 100501212, 1, 0, 1224, 0, 0, 65535, 0, 10, NULL, 100501212, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (203, 120001481, 1, 0, 1224, 0, 0, 65535, 0, 0, NULL, 120001481, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (204, 120001481, 1, 0, 1224, 0, 0, 65535, 0, 0, NULL, 120001481, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (205, 121001355, 1, 0, 1224, 0, 0, 65535, 0, 0, NULL, 121001355, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (206, 100000001, 1, 0, 1232, 0, 0, 134, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (207, 100000001, 1, 0, 1232, 0, 0, 133, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (208, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (209, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (210, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (211, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (212, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (213, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (214, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (215, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (216, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (217, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (218, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (219, 100000001, 1, 0, 1232, 0, 0, 65535, 0, 0, 'Tata', 100000001, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (220, 122001624, 1, 0, 1224, 0, 0, 65535, 0, 0, NULL, 122001624, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (221, 122001624, 1, 0, 1224, 0, 0, 65535, 0, 0, NULL, 122001624, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (222, 125003353, 1, 0, 1224, 0, 0, 65535, 0, 0, NULL, 125003353, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (223, 123001404, 1, 0, 1224, 0, 0, 65535, 0, 0, NULL, 123001404, 0, 0, 0, 0, 0, NULL, 0, 0, 0, 0);

-- ----------------------------
-- Table structure for item_cooldowns
-- ----------------------------
DROP TABLE IF EXISTS `item_cooldowns`;
CREATE TABLE `item_cooldowns`  (
  `player_id` int(11) NOT NULL,
  `delay_id` int(11) NOT NULL,
  `use_delay` smallint(5) UNSIGNED NOT NULL,
  `reuse_time` bigint(13) NOT NULL,
  PRIMARY KEY (`player_id`, `delay_id`) USING BTREE,
  CONSTRAINT `item_cooldowns_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of item_cooldowns
-- ----------------------------

-- ----------------------------
-- Table structure for item_stones
-- ----------------------------
DROP TABLE IF EXISTS `item_stones`;
CREATE TABLE `item_stones`  (
  `itemUniqueId` int(11) NOT NULL,
  `itemId` int(11) NOT NULL,
  `slot` int(2) NOT NULL,
  `category` int(2) NOT NULL DEFAULT 0,
  PRIMARY KEY (`itemUniqueId`, `slot`, `category`) USING BTREE,
  CONSTRAINT `item_stones_ibfk_1` FOREIGN KEY (`itemUniqueId`) REFERENCES `inventory` (`itemUniqueId`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of item_stones
-- ----------------------------
INSERT INTO `item_stones` VALUES (46, 1, 0, 6);
INSERT INTO `item_stones` VALUES (47, 1, 0, 6);
INSERT INTO `item_stones` VALUES (48, 1, 0, 6);
INSERT INTO `item_stones` VALUES (49, 1, 0, 6);
INSERT INTO `item_stones` VALUES (50, 1, 0, 6);
INSERT INTO `item_stones` VALUES (51, 1, 0, 6);
INSERT INTO `item_stones` VALUES (52, 1, 0, 6);
INSERT INTO `item_stones` VALUES (53, 1, 0, 6);
INSERT INTO `item_stones` VALUES (54, 1, 0, 6);
INSERT INTO `item_stones` VALUES (55, 1, 0, 6);
INSERT INTO `item_stones` VALUES (56, 1, 0, 6);
INSERT INTO `item_stones` VALUES (57, 1, 0, 6);
INSERT INTO `item_stones` VALUES (58, 1, 0, 6);
INSERT INTO `item_stones` VALUES (59, 1, 0, 6);
INSERT INTO `item_stones` VALUES (60, 1, 0, 6);
INSERT INTO `item_stones` VALUES (61, 1, 0, 6);
INSERT INTO `item_stones` VALUES (62, 1, 0, 6);
INSERT INTO `item_stones` VALUES (63, 1, 0, 6);
INSERT INTO `item_stones` VALUES (64, 1, 0, 6);
INSERT INTO `item_stones` VALUES (65, 1, 0, 6);
INSERT INTO `item_stones` VALUES (69, 1, 0, 6);
INSERT INTO `item_stones` VALUES (71, 1, 0, 6);
INSERT INTO `item_stones` VALUES (72, 1, 0, 6);
INSERT INTO `item_stones` VALUES (73, 1, 0, 6);
INSERT INTO `item_stones` VALUES (74, 1, 0, 6);
INSERT INTO `item_stones` VALUES (75, 1, 0, 6);
INSERT INTO `item_stones` VALUES (76, 1, 0, 6);
INSERT INTO `item_stones` VALUES (77, 1, 0, 6);
INSERT INTO `item_stones` VALUES (78, 1, 0, 6);
INSERT INTO `item_stones` VALUES (79, 1, 0, 6);
INSERT INTO `item_stones` VALUES (80, 1, 0, 6);
INSERT INTO `item_stones` VALUES (81, 1, 0, 6);
INSERT INTO `item_stones` VALUES (82, 1, 0, 6);
INSERT INTO `item_stones` VALUES (83, 1, 0, 6);
INSERT INTO `item_stones` VALUES (84, 1, 0, 6);
INSERT INTO `item_stones` VALUES (85, 1, 0, 6);
INSERT INTO `item_stones` VALUES (86, 1, 0, 6);
INSERT INTO `item_stones` VALUES (87, 1, 0, 6);
INSERT INTO `item_stones` VALUES (88, 1, 0, 6);
INSERT INTO `item_stones` VALUES (89, 1, 0, 6);
INSERT INTO `item_stones` VALUES (90, 1, 0, 6);
INSERT INTO `item_stones` VALUES (91, 1, 0, 6);
INSERT INTO `item_stones` VALUES (92, 1, 0, 6);
INSERT INTO `item_stones` VALUES (93, 1, 0, 6);
INSERT INTO `item_stones` VALUES (94, 1, 0, 6);
INSERT INTO `item_stones` VALUES (95, 1, 0, 6);
INSERT INTO `item_stones` VALUES (96, 1, 0, 6);
INSERT INTO `item_stones` VALUES (97, 1, 0, 6);
INSERT INTO `item_stones` VALUES (98, 1, 0, 6);
INSERT INTO `item_stones` VALUES (99, 1, 0, 6);
INSERT INTO `item_stones` VALUES (100, 1, 0, 6);
INSERT INTO `item_stones` VALUES (101, 1, 0, 6);
INSERT INTO `item_stones` VALUES (102, 1, 0, 6);
INSERT INTO `item_stones` VALUES (103, 1, 0, 6);
INSERT INTO `item_stones` VALUES (104, 1, 0, 6);
INSERT INTO `item_stones` VALUES (105, 1, 0, 6);
INSERT INTO `item_stones` VALUES (106, 1, 0, 6);
INSERT INTO `item_stones` VALUES (107, 1, 0, 6);
INSERT INTO `item_stones` VALUES (108, 1, 0, 6);
INSERT INTO `item_stones` VALUES (109, 1, 0, 6);
INSERT INTO `item_stones` VALUES (110, 1, 0, 6);
INSERT INTO `item_stones` VALUES (111, 1, 0, 6);
INSERT INTO `item_stones` VALUES (112, 1, 0, 6);
INSERT INTO `item_stones` VALUES (113, 1, 0, 6);
INSERT INTO `item_stones` VALUES (114, 1, 0, 6);
INSERT INTO `item_stones` VALUES (115, 1, 0, 6);
INSERT INTO `item_stones` VALUES (116, 1, 0, 6);
INSERT INTO `item_stones` VALUES (117, 1, 0, 6);
INSERT INTO `item_stones` VALUES (118, 1, 0, 6);
INSERT INTO `item_stones` VALUES (119, 1, 0, 6);
INSERT INTO `item_stones` VALUES (120, 1, 0, 6);
INSERT INTO `item_stones` VALUES (121, 1, 0, 6);
INSERT INTO `item_stones` VALUES (122, 1, 0, 6);
INSERT INTO `item_stones` VALUES (123, 1, 0, 6);
INSERT INTO `item_stones` VALUES (124, 1, 0, 6);
INSERT INTO `item_stones` VALUES (125, 1, 0, 6);
INSERT INTO `item_stones` VALUES (126, 1, 0, 6);
INSERT INTO `item_stones` VALUES (127, 1, 0, 6);
INSERT INTO `item_stones` VALUES (128, 1, 0, 6);
INSERT INTO `item_stones` VALUES (129, 1, 0, 6);
INSERT INTO `item_stones` VALUES (130, 1, 0, 6);
INSERT INTO `item_stones` VALUES (131, 1, 0, 6);
INSERT INTO `item_stones` VALUES (132, 1, 0, 6);
INSERT INTO `item_stones` VALUES (133, 1, 0, 6);
INSERT INTO `item_stones` VALUES (134, 1, 0, 6);
INSERT INTO `item_stones` VALUES (135, 1, 0, 6);
INSERT INTO `item_stones` VALUES (136, 1, 0, 6);
INSERT INTO `item_stones` VALUES (137, 1, 0, 6);
INSERT INTO `item_stones` VALUES (138, 1, 0, 6);
INSERT INTO `item_stones` VALUES (139, 1, 0, 6);
INSERT INTO `item_stones` VALUES (140, 1, 0, 6);
INSERT INTO `item_stones` VALUES (141, 1, 0, 6);
INSERT INTO `item_stones` VALUES (142, 1, 0, 6);
INSERT INTO `item_stones` VALUES (143, 1, 0, 6);
INSERT INTO `item_stones` VALUES (144, 1, 0, 6);
INSERT INTO `item_stones` VALUES (145, 1, 0, 6);
INSERT INTO `item_stones` VALUES (146, 1, 0, 6);
INSERT INTO `item_stones` VALUES (147, 1, 0, 6);
INSERT INTO `item_stones` VALUES (148, 1, 0, 6);
INSERT INTO `item_stones` VALUES (149, 1, 0, 6);
INSERT INTO `item_stones` VALUES (150, 1, 0, 6);
INSERT INTO `item_stones` VALUES (151, 1, 0, 6);
INSERT INTO `item_stones` VALUES (152, 1, 0, 6);
INSERT INTO `item_stones` VALUES (153, 1, 0, 6);
INSERT INTO `item_stones` VALUES (154, 1, 0, 6);
INSERT INTO `item_stones` VALUES (155, 1, 0, 6);
INSERT INTO `item_stones` VALUES (156, 1, 0, 6);
INSERT INTO `item_stones` VALUES (157, 1, 0, 6);
INSERT INTO `item_stones` VALUES (158, 1, 0, 6);
INSERT INTO `item_stones` VALUES (159, 1, 0, 6);
INSERT INTO `item_stones` VALUES (160, 1, 0, 6);
INSERT INTO `item_stones` VALUES (161, 1, 0, 6);
INSERT INTO `item_stones` VALUES (162, 1, 0, 6);
INSERT INTO `item_stones` VALUES (163, 1, 0, 6);
INSERT INTO `item_stones` VALUES (164, 1, 0, 6);
INSERT INTO `item_stones` VALUES (165, 1, 0, 6);
INSERT INTO `item_stones` VALUES (166, 1, 0, 6);
INSERT INTO `item_stones` VALUES (167, 1, 0, 6);
INSERT INTO `item_stones` VALUES (168, 1, 0, 6);
INSERT INTO `item_stones` VALUES (169, 1, 0, 6);
INSERT INTO `item_stones` VALUES (170, 1, 0, 6);
INSERT INTO `item_stones` VALUES (171, 1, 0, 6);
INSERT INTO `item_stones` VALUES (172, 1, 0, 6);
INSERT INTO `item_stones` VALUES (173, 1, 0, 6);
INSERT INTO `item_stones` VALUES (174, 1, 0, 6);
INSERT INTO `item_stones` VALUES (175, 1, 0, 6);
INSERT INTO `item_stones` VALUES (176, 1, 0, 6);
INSERT INTO `item_stones` VALUES (177, 1, 0, 6);
INSERT INTO `item_stones` VALUES (178, 1, 0, 6);
INSERT INTO `item_stones` VALUES (179, 1, 0, 6);
INSERT INTO `item_stones` VALUES (180, 1, 0, 6);
INSERT INTO `item_stones` VALUES (181, 1, 0, 6);
INSERT INTO `item_stones` VALUES (182, 1, 0, 6);
INSERT INTO `item_stones` VALUES (183, 1, 0, 6);
INSERT INTO `item_stones` VALUES (184, 1, 0, 6);
INSERT INTO `item_stones` VALUES (185, 1, 0, 6);
INSERT INTO `item_stones` VALUES (186, 1, 0, 6);
INSERT INTO `item_stones` VALUES (187, 1, 0, 6);
INSERT INTO `item_stones` VALUES (188, 1, 0, 6);
INSERT INTO `item_stones` VALUES (189, 1, 0, 6);
INSERT INTO `item_stones` VALUES (190, 1, 0, 6);
INSERT INTO `item_stones` VALUES (191, 1, 0, 6);
INSERT INTO `item_stones` VALUES (192, 1, 0, 6);
INSERT INTO `item_stones` VALUES (193, 1, 0, 6);
INSERT INTO `item_stones` VALUES (194, 1, 0, 6);
INSERT INTO `item_stones` VALUES (195, 1, 0, 6);
INSERT INTO `item_stones` VALUES (196, 1, 0, 6);
INSERT INTO `item_stones` VALUES (197, 1, 0, 6);
INSERT INTO `item_stones` VALUES (198, 1, 0, 6);
INSERT INTO `item_stones` VALUES (199, 1, 0, 6);
INSERT INTO `item_stones` VALUES (206, 1, 0, 6);
INSERT INTO `item_stones` VALUES (207, 1, 0, 6);
INSERT INTO `item_stones` VALUES (208, 1, 0, 6);
INSERT INTO `item_stones` VALUES (209, 1, 0, 6);
INSERT INTO `item_stones` VALUES (210, 1, 0, 6);
INSERT INTO `item_stones` VALUES (211, 1, 0, 6);
INSERT INTO `item_stones` VALUES (212, 1, 0, 6);
INSERT INTO `item_stones` VALUES (213, 1, 0, 6);
INSERT INTO `item_stones` VALUES (214, 1, 0, 6);
INSERT INTO `item_stones` VALUES (215, 1, 0, 6);
INSERT INTO `item_stones` VALUES (216, 1, 0, 6);
INSERT INTO `item_stones` VALUES (217, 1, 0, 6);
INSERT INTO `item_stones` VALUES (218, 1, 0, 6);
INSERT INTO `item_stones` VALUES (219, 1, 0, 6);

-- ----------------------------
-- Table structure for ladder_player
-- ----------------------------
DROP TABLE IF EXISTS `ladder_player`;
CREATE TABLE `ladder_player`  (
  `player_id` int(11) NOT NULL,
  `rating` int(11) NULL DEFAULT 1000,
  `wins` int(11) NULL DEFAULT NULL,
  `losses` int(11) NULL DEFAULT NULL,
  `leaves` int(11) NULL DEFAULT NULL,
  `rank` int(11) NOT NULL DEFAULT -1,
  `last_rank` int(11) NOT NULL DEFAULT -1,
  `last_update` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`player_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ladder_player
-- ----------------------------

-- ----------------------------
-- Table structure for legion_announcement_list
-- ----------------------------
DROP TABLE IF EXISTS `legion_announcement_list`;
CREATE TABLE `legion_announcement_list`  (
  `legion_id` int(11) NOT NULL,
  `announcement` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `date` timestamp NOT NULL DEFAULT current_timestamp(),
  INDEX `legion_id`(`legion_id`) USING BTREE,
  CONSTRAINT `legion_announcement_list_ibfk_1` FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legion_announcement_list
-- ----------------------------

-- ----------------------------
-- Table structure for legion_emblems
-- ----------------------------
DROP TABLE IF EXISTS `legion_emblems`;
CREATE TABLE `legion_emblems`  (
  `legion_id` int(11) NOT NULL,
  `emblem_ver` int(3) NOT NULL DEFAULT 0,
  `color_r` int(3) NOT NULL DEFAULT 0,
  `color_g` int(3) NOT NULL DEFAULT 0,
  `color_b` int(3) NOT NULL DEFAULT 0,
  `custom` tinyint(1) NOT NULL DEFAULT 0,
  `emblem_data` longblob NULL DEFAULT NULL,
  PRIMARY KEY (`legion_id`) USING BTREE,
  CONSTRAINT `legion_emblems_ibfk_1` FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legion_emblems
-- ----------------------------

-- ----------------------------
-- Table structure for legion_history
-- ----------------------------
DROP TABLE IF EXISTS `legion_history`;
CREATE TABLE `legion_history`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `legion_id` int(11) NOT NULL,
  `date` timestamp NOT NULL DEFAULT current_timestamp(),
  `history_type` enum('CREATE','JOIN','KICK','LEVEL_UP','APPOINTED','EMBLEM_REGISTER','EMBLEM_MODIFIED') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `legion_id`(`legion_id`) USING BTREE,
  CONSTRAINT `legion_history_ibfk_1` FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legion_history
-- ----------------------------

-- ----------------------------
-- Table structure for legion_members
-- ----------------------------
DROP TABLE IF EXISTS `legion_members`;
CREATE TABLE `legion_members`  (
  `legion_id` int(11) NOT NULL,
  `player_id` int(11) NOT NULL,
  `nickname` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `rank` enum('BRIGADE_GENERAL','CENTURION','LEGIONARY') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'LEGIONARY',
  `selfintro` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  PRIMARY KEY (`player_id`) USING BTREE,
  INDEX `player_id`(`player_id`) USING BTREE,
  INDEX `legion_id`(`legion_id`) USING BTREE,
  CONSTRAINT `legion_members_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `legion_members_ibfk_2` FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legion_members
-- ----------------------------

-- ----------------------------
-- Table structure for legions
-- ----------------------------
DROP TABLE IF EXISTS `legions`;
CREATE TABLE `legions`  (
  `id` int(11) NOT NULL,
  `name` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `rank` int(11) NOT NULL DEFAULT 0,
  `oldrank` int(11) NOT NULL DEFAULT 0,
  `level` int(1) NOT NULL DEFAULT 1,
  `contribution_points` int(11) NOT NULL DEFAULT 0,
  `deputy_permission1` int(4) NOT NULL DEFAULT 12,
  `deputy_permission2` int(4) NOT NULL DEFAULT 30,
  `centurion_permission1` int(4) NOT NULL DEFAULT 8,
  `centurion_permission2` int(4) NOT NULL DEFAULT 28,
  `legionary_permission1` int(4) NOT NULL DEFAULT 0,
  `legionary_permission2` int(4) NOT NULL DEFAULT 24,
  `volunteer_permission1` int(4) NOT NULL DEFAULT 0,
  `volunteer_permission2` int(4) NOT NULL DEFAULT 8,
  `disband_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name_unique`(`name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legions
-- ----------------------------

-- ----------------------------
-- Table structure for mail
-- ----------------------------
DROP TABLE IF EXISTS `mail`;
CREATE TABLE `mail`  (
  `mailUniqueId` int(11) NOT NULL,
  `mailRecipientId` int(11) NOT NULL,
  `senderName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `mailTitle` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `mailMessage` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `unread` tinyint(4) NOT NULL DEFAULT 1,
  `attachedItemId` int(11) NOT NULL,
  `attachedKinahCount` bigint(20) NOT NULL,
  `express` tinyint(4) NOT NULL DEFAULT 0,
  `recievedTime` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP,
  `temperance` int(11) NULL DEFAULT 0,
  `mail_type` tinyint(4) NULL DEFAULT 0,
  PRIMARY KEY (`mailUniqueId`) USING BTREE,
  INDEX `mailRecipientId`(`mailRecipientId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mail
-- ----------------------------
INSERT INTO `mail` VALUES (12, 1221, 'Donation Shop', 'Item Purchase', 'You have purchased: Premium Health Potion (x10) for 15 DP. Enjoy your item!', 0, 0, 0, 1, '2025-07-21 05:51:07', 0, 0);

-- ----------------------------
-- Table structure for might
-- ----------------------------
DROP TABLE IF EXISTS `might`;
CREATE TABLE `might`  (
  `account_id` int(11) NOT NULL,
  `might` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`account_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of might
-- ----------------------------
INSERT INTO `might` VALUES (3, 3760);
INSERT INTO `might` VALUES (6, 0);

-- ----------------------------
-- Table structure for motions
-- ----------------------------
DROP TABLE IF EXISTS `motions`;
CREATE TABLE `motions`  (
  `player_id` int(11) NOT NULL,
  `motion_idle` int(11) NOT NULL DEFAULT 0,
  `motion_run` int(11) NOT NULL DEFAULT 0,
  `motion_jump` int(11) NOT NULL DEFAULT 0,
  `motion_rest` int(11) NOT NULL DEFAULT 0,
  `motion_shop` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`) USING BTREE,
  CONSTRAINT `motions_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of motions
-- ----------------------------
INSERT INTO `motions` VALUES (1221, 0, 0, 0, 0, 0);
INSERT INTO `motions` VALUES (1224, 0, 0, 0, 0, 0);
INSERT INTO `motions` VALUES (1232, 0, 0, 0, 0, 0);
INSERT INTO `motions` VALUES (1300, 0, 0, 0, 0, 0);

-- ----------------------------
-- Table structure for npc_shouts
-- ----------------------------
DROP TABLE IF EXISTS `npc_shouts`;
CREATE TABLE `npc_shouts`  (
  `npc_id` int(11) NOT NULL,
  `message_id` int(11) NOT NULL,
  `_interval` int(11) NOT NULL,
  PRIMARY KEY (`npc_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of npc_shouts
-- ----------------------------

-- ----------------------------
-- Table structure for petitions
-- ----------------------------
DROP TABLE IF EXISTS `petitions`;
CREATE TABLE `petitions`  (
  `id` bigint(11) NOT NULL,
  `playerId` int(11) NOT NULL,
  `type` int(11) NOT NULL,
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `message` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `addData` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `time` bigint(11) NOT NULL DEFAULT 0,
  `status` enum('PENDING','IN_PROGRESS','REPLIED') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of petitions
-- ----------------------------

-- ----------------------------
-- Table structure for player_appearance
-- ----------------------------
DROP TABLE IF EXISTS `player_appearance`;
CREATE TABLE `player_appearance`  (
  `player_id` int(11) NOT NULL,
  `face` int(11) NOT NULL,
  `hair` int(11) NOT NULL,
  `deco` int(11) NOT NULL,
  `tattoo` int(11) NOT NULL,
  `facial_contour` int(11) NOT NULL,
  `skin_rgb` int(11) NOT NULL,
  `hair_rgb` int(11) NOT NULL,
  `lip_rgb` int(11) NOT NULL,
  `eye_rgb` int(11) NOT NULL,
  `expression` int(11) NOT NULL,
  `face_shape` int(11) NOT NULL,
  `jaw_line` int(11) NOT NULL,
  `forehead` int(11) NOT NULL,
  `eye_height` int(11) NOT NULL,
  `eye_space` int(11) NOT NULL,
  `eye_width` int(11) NOT NULL,
  `eye_size` int(11) NOT NULL,
  `eye_shape` int(11) NOT NULL,
  `eye_angle` int(11) NOT NULL,
  `brow_height` int(11) NOT NULL,
  `brow_angle` int(11) NOT NULL,
  `brow_shape` int(11) NOT NULL,
  `nose` int(11) NOT NULL,
  `nose_bridge` int(11) NOT NULL,
  `nose_width` int(11) NOT NULL,
  `nose_tip` int(11) NOT NULL,
  `cheek` int(11) NOT NULL,
  `lip_height` int(11) NOT NULL,
  `mouth_size` int(11) NOT NULL,
  `lip_size` int(11) NOT NULL,
  `smile` int(11) NOT NULL,
  `lip_shape` int(11) NOT NULL,
  `jaw_height` int(11) NOT NULL,
  `chin_jut` int(11) NOT NULL,
  `ear_shape` int(11) NOT NULL,
  `head_size` int(11) NOT NULL,
  `neck` int(11) NOT NULL,
  `neck_length` int(11) NOT NULL,
  `shoulders` int(11) NOT NULL,
  `shoulder_size` int(11) NOT NULL,
  `torso` int(11) NOT NULL,
  `chest` int(11) NOT NULL,
  `waist` int(11) NOT NULL,
  `hips` int(11) NOT NULL,
  `arm_thickness` int(11) NOT NULL,
  `arm_length` int(11) NOT NULL,
  `hand_size` int(11) NOT NULL,
  `leg_thickness` int(11) NOT NULL,
  `leg_length` int(11) NOT NULL,
  `foot_size` int(11) NOT NULL,
  `facial_rate` int(11) NOT NULL,
  `voice` int(11) NOT NULL,
  `height` float NOT NULL,
  PRIMARY KEY (`player_id`) USING BTREE,
  CONSTRAINT `player_id_fk` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_appearance
-- ----------------------------
INSERT INTO `player_appearance` VALUES (1221, 9, 1, 0, 4, 0, 12898035, 6844591, 13159398, 15781834, 0, 0, 46, 155, 79, 158, 158, 183, 153, 153, 16, 162, 182, 203, 100, 55, 151, 168, 0, 153, 18, 182, 7, 16, 0, 46, 3, 254, 243, 249, 206, 247, 243, 249, 0, 249, 0, 247, 11, 0, 237, 0, 0, 0.818558);
INSERT INTO `player_appearance` VALUES (1224, 8, 40, 1, 0, 0, 12569330, 6579884, 13486567, 12169132, 0, 0, 74, 0, 62, 143, 148, 135, 43, 155, 151, 178, 188, 171, 0, 162, 18, 0, 138, 162, 31, 195, 0, 135, 159, 0, 251, 255, 247, 249, 243, 254, 0, 0, 0, 0, 254, 0, 0, 0, 0, 9, 0, 0.969497);
INSERT INTO `player_appearance` VALUES (1232, 18, 32, 0, 0, 0, 13817835, 10336463, 13948396, 8693904, 0, 0, 5, 0, 34, 16, 138, 0, 20, 174, 43, 25, 159, 228, 0, 18, 25, 184, 75, 153, 0, 149, 0, 151, 0, 0, 3, 254, 243, 249, 206, 247, 243, 249, 0, 249, 0, 247, 11, 0, 237, 0, 0, 0.818558);
INSERT INTO `player_appearance` VALUES (1300, 11, 22, 0, 0, 0, ********, ********, ********, ********, 0, 0, 100, 0, 81, 182, 184, 228, 0, 0, 138, 7, 158, 135, 61, 28, 138, 184, 138, 34, 17, 188, 0, 133, 34, 0, 3, 254, 243, 249, 206, 247, 243, 249, 0, 249, 0, 247, 11, 0, 237, 9, 0, 0.818558);

-- ----------------------------
-- Table structure for player_clients
-- ----------------------------
DROP TABLE IF EXISTS `player_clients`;
CREATE TABLE `player_clients`  (
  `account_id` int(11) NOT NULL,
  `mac` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `version` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`account_id`) USING BTREE,
  CONSTRAINT `player_clients_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_clients
-- ----------------------------
INSERT INTO `player_clients` VALUES (3, 'A8-3B-76-24-C8-24', 'CF-1252-6-2');

-- ----------------------------
-- Table structure for player_dailies
-- ----------------------------
DROP TABLE IF EXISTS `player_dailies`;
CREATE TABLE `player_dailies`  (
  `player_id` int(11) NOT NULL COMMENT 'Player object ID from players table',
  `mode` int(11) NOT NULL COMMENT 'PvP mode type (see GloryService.PvPMode enum)',
  `value` int(11) NOT NULL DEFAULT 0 COMMENT 'Daily accumulated value for this PvP mode',
  `last_reset` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last time this record was reset',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'When this record was created',
  PRIMARY KEY (`player_id`, `mode`) USING BTREE,
  INDEX `idx_player_id`(`player_id`) USING BTREE,
  INDEX `idx_mode`(`mode`) USING BTREE,
  INDEX `idx_last_reset`(`last_reset`) USING BTREE,
  CONSTRAINT `fk_player_dailies_player_id` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'Daily PvP tracking for players' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_dailies
-- ----------------------------

-- ----------------------------
-- Table structure for player_effects
-- ----------------------------
DROP TABLE IF EXISTS `player_effects`;
CREATE TABLE `player_effects`  (
  `player_id` int(11) NOT NULL,
  `skill_id` int(11) NOT NULL,
  `skill_lvl` tinyint(4) NOT NULL,
  `current_time` int(11) NOT NULL,
  `reuse_delay` bigint(13) NOT NULL,
  PRIMARY KEY (`player_id`, `skill_id`) USING BTREE,
  CONSTRAINT `player_effects_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_effects
-- ----------------------------
INSERT INTO `player_effects` VALUES (1221, 9959, 3, 280764, 0);

-- ----------------------------
-- Table structure for player_life_stats
-- ----------------------------
DROP TABLE IF EXISTS `player_life_stats`;
CREATE TABLE `player_life_stats`  (
  `player_id` int(11) NOT NULL,
  `hp` int(11) NOT NULL DEFAULT 1,
  `mp` int(11) NOT NULL DEFAULT 1,
  `fp` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`player_id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Fixed;

-- ----------------------------
-- Records of player_life_stats
-- ----------------------------
INSERT INTO `player_life_stats` VALUES (1232, 6817, 9979, 60);
INSERT INTO `player_life_stats` VALUES (1221, 6222, 4405, 60);
INSERT INTO `player_life_stats` VALUES (1300, 8807, 5622, 60);
INSERT INTO `player_life_stats` VALUES (1224, 458, 1000, 60);

-- ----------------------------
-- Table structure for player_macrosses
-- ----------------------------
DROP TABLE IF EXISTS `player_macrosses`;
CREATE TABLE `player_macrosses`  (
  `player_id` int(11) NOT NULL,
  `order` int(3) NOT NULL,
  `macro` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  UNIQUE INDEX `main`(`player_id`, `order`) USING BTREE,
  CONSTRAINT `player_macrosses_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_macrosses
-- ----------------------------

-- ----------------------------
-- Table structure for player_motions
-- ----------------------------
DROP TABLE IF EXISTS `player_motions`;
CREATE TABLE `player_motions`  (
  `player_id` int(11) NOT NULL,
  `motion_id` int(11) NOT NULL,
  `motion_expires_time` bigint(20) NOT NULL DEFAULT 0,
  `motion_date` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`player_id`, `motion_id`) USING BTREE,
  CONSTRAINT `player_motions_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_motions
-- ----------------------------

-- ----------------------------
-- Table structure for player_passkey
-- ----------------------------
DROP TABLE IF EXISTS `player_passkey`;
CREATE TABLE `player_passkey`  (
  `account_id` int(11) NOT NULL,
  `passkey` varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`account_id`, `passkey`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_passkey
-- ----------------------------

-- ----------------------------
-- Table structure for player_pets
-- ----------------------------
DROP TABLE IF EXISTS `player_pets`;
CREATE TABLE `player_pets`  (
  `idx` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` int(11) NOT NULL,
  `pet_id` int(11) NOT NULL,
  `decoration` int(11) NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`idx`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_pets
-- ----------------------------

-- ----------------------------
-- Table structure for player_punishments
-- ----------------------------
DROP TABLE IF EXISTS `player_punishments`;
CREATE TABLE `player_punishments`  (
  `player_id` int(11) NOT NULL,
  `punishment_status` tinyint(3) UNSIGNED NULL DEFAULT 0,
  `punishment_timer` int(10) UNSIGNED NULL DEFAULT 0,
  PRIMARY KEY (`player_id`) USING BTREE,
  CONSTRAINT `player_punishments_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_punishments
-- ----------------------------

-- ----------------------------
-- Table structure for player_quests
-- ----------------------------
DROP TABLE IF EXISTS `player_quests`;
CREATE TABLE `player_quests`  (
  `player_id` int(11) NOT NULL,
  `quest_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `status` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'NONE',
  `quest_vars` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `complete_count` int(3) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`, `quest_id`) USING BTREE,
  CONSTRAINT `player_quests_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_quests
-- ----------------------------
INSERT INTO `player_quests` VALUES (1221, 1006, 'COMPLETE', 0, 0);
INSERT INTO `player_quests` VALUES (1221, 1007, 'COMPLETE', 0, 0);
INSERT INTO `player_quests` VALUES (1221, 1920, 'START', 0, 0);
INSERT INTO `player_quests` VALUES (1221, 1929, 'COMPLETE', 0, 0);
INSERT INTO `player_quests` VALUES (1221, 10000, 'START', 0, 0);
INSERT INTO `player_quests` VALUES (1232, 1006, 'COMPLETE', 0, 0);
INSERT INTO `player_quests` VALUES (1232, 1007, 'COMPLETE', 0, 0);
INSERT INTO `player_quests` VALUES (1232, 1920, 'START', 0, 0);
INSERT INTO `player_quests` VALUES (1232, 1929, 'COMPLETE', 0, 0);
INSERT INTO `player_quests` VALUES (1232, 10000, 'START', 0, 0);
INSERT INTO `player_quests` VALUES (1300, 1006, 'COMPLETE', 0, 0);
INSERT INTO `player_quests` VALUES (1300, 1007, 'COMPLETE', 0, 0);
INSERT INTO `player_quests` VALUES (1300, 1920, 'START', 0, 0);
INSERT INTO `player_quests` VALUES (1300, 1929, 'COMPLETE', 0, 0);
INSERT INTO `player_quests` VALUES (1300, 10000, 'START', 0, 0);

-- ----------------------------
-- Table structure for player_recipes
-- ----------------------------
DROP TABLE IF EXISTS `player_recipes`;
CREATE TABLE `player_recipes`  (
  `player_id` int(11) NOT NULL,
  `recipe_id` int(11) NOT NULL,
  PRIMARY KEY (`player_id`, `recipe_id`) USING BTREE,
  CONSTRAINT `player_recipes_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_recipes
-- ----------------------------
INSERT INTO `player_recipes` VALUES (1224, 155000001);
INSERT INTO `player_recipes` VALUES (1224, 155000002);
INSERT INTO `player_recipes` VALUES (1224, 155000005);
INSERT INTO `player_recipes` VALUES (1232, 155000001);
INSERT INTO `player_recipes` VALUES (1232, 155000002);
INSERT INTO `player_recipes` VALUES (1232, 155000005);

-- ----------------------------
-- Table structure for player_settings
-- ----------------------------
DROP TABLE IF EXISTS `player_settings`;
CREATE TABLE `player_settings`  (
  `player_id` int(11) NOT NULL,
  `settings_type` int(11) NOT NULL,
  `settings` blob NULL DEFAULT NULL,
  PRIMARY KEY (`player_id`, `settings_type`) USING BTREE,
  CONSTRAINT `player_settings_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_settings
-- ----------------------------
INSERT INTO `player_settings` VALUES (1221, 0, 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);
INSERT INTO `player_settings` VALUES (1221, 1, 0x1A01000092030000789C95935D4EC33010843F5E91B843C43B244E4A9A48A5157F3D013C5734204094141A403D23A762948015DB29125AC9F6EC8C676DAFFCC51E13666C796145C427F76C6878624DCD2987188E493447626A2AE5EFC4D63CB4EC0DD7CC39922A179A31E5807D39AE79E5FDC7A5B1D98A476EDBDD0DCF9A578AA9769EA8423F320F1B07151EEB4741A9F1C2E231A9C64B8B4BCE1C9CB4FCEEC8B972FCFC2855A1CFCFDBF3F6F57FFB1BCE833BBABC71CE3BF6B01F13E21D2FDD75E18D0F652AE597D26C5838DD5AA8CFDB9E76A975A2EEA7AA1AD94EE56D958EFD5519ABCA34165A879AD4D194F20B3559506DC8696455A9D6433E65E063064F1DDE6E34A8334EC5ACA7E9DE2AFED7CBC6837F04BE01F5BA72FE);
INSERT INTO `player_settings` VALUES (1221, 2, 0x30);
INSERT INTO `player_settings` VALUES (1221, 3, 0x30);
INSERT INTO `player_settings` VALUES (1221, 4, 0xA70000007E010000789C9D90CF0EC1301CC73FAE12EFB0B8332E4E6C374FC059D061C9D62E8AF08A9ECAB79D4884D3D243FBFBFE6DFBA4C79C9C3B351509370ACE784A1C960543A68C99684FC458F6C28D58CB31B26B562C194935D3949331A0AF4447C3E59DE23FE849F335A607FF86035B353A359652174276E28DD643E74AA8179EC99976F6B6CDC165C45B71E10D4E8A70BF3A3A7FB3BEBBBBBBDBF6F4EF7FC00B76C044AC);
INSERT INTO `player_settings` VALUES (1232, 0, 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);
INSERT INTO `player_settings` VALUES (1232, 1, 0x0501000022030000789C9592D14AC33014863F6F05DFA178AF6DD26E6C3037D8C59E40AF87AB4365B3D35565CFE853F993DAD8921491439BE49C2FFF9F9CF68B33662C38F1C29E844FB61CA979E640C50D9718AEC93426AA5494CA3FA85AF1E8AA77DCB2E24AD458AB05732E3897E28157DE7F546A9F2D79E2DEEDAED969DC2BE6DA39924337EC1FEB364C34BBA4D0BBF0EB951CB29EC7905EABBA1CD4CE9CB2EDE94D14597087DF98910EDCBCE9CA1B1FCA94CA6FC41C59F7BAB756DF4F1D76A379A6AF6175C2C4BB8E9D4B536D29E329A371A227646C87C91D153279E016532A3C65351F458869A063A2A70E6F574439D373CC3B4CD3ABF45F9D4DA3FF2C7C03E90D67AF);
INSERT INTO `player_settings` VALUES (1232, 2, 0x30);
INSERT INTO `player_settings` VALUES (1232, 3, 0x30);
INSERT INTO `player_settings` VALUES (1232, 4, 0xA70000007E010000789C9D90CF0EC1301CC73FAE12EFB0B8332E4E6C374FC059D061C9D62E8AF08A9ECAB79D4884D3D243FBFBFE6DFBA4C79C9C3B351509370ACE784A1C960543A68C99684FC458F6C28D58CB31B26B562C194935D3949331A0AF4447C3E59DE23FE849F335A607FF86035B353A359652174276E28DD643E74AA8179EC99976F6B6CDC165C45B71E10D4E8A70BF3A3A7FB3BEBBBBBBDBF6F4EF7FC00B76C044AC);
INSERT INTO `player_settings` VALUES (1300, 0, 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);
INSERT INTO `player_settings` VALUES (1300, 1, 0x1A01000092030000789C95935D4EC33010843F5E91B843C43B244E4A9A48A5157F3D013C5734204094141A403D23A762948015DB29125AC9F6EC8C676DAFFCC51E13666C796145C427F76C6878624DCD2987188E493447626A2AE5EFC4D63CB4EC0DD7CC39922A179A31E5807D39AE79E5FDC7A5B1D98A476EDBDD0DCF9A578AA9769EA8423F320F1B07151EEB4741A9F1C2E231A9C64B8B4BCE1C9CB4FCEEC8B972FCFC2855A1CFCFDBF3F6F57FFB1BCE833BBABC71CE3BF6B01F13E21D2FDD75E18D0F652AE597D26C5838DD5AA8CFDB9E76A975A2EEA7AA1AD94EE56D958EFD5519ABCA34165A879AD4D194F20B3559506DC8696455A9D6433E65E063064F1DDE6E34A8334EC5ACA7E9DE2AFED7CBC6837F04BE01F5BA72FE);
INSERT INTO `player_settings` VALUES (1300, 2, 0x30);
INSERT INTO `player_settings` VALUES (1300, 3, 0x30);
INSERT INTO `player_settings` VALUES (1300, 4, 0xA70000007E010000789C9D90CF0EC1301CC73FAE12EFB0B8332E4E6C374FC059D061C9D62E8AF08A9ECAB79D4884D3D243FBFBFE6DFBA4C79C9C3B351509370ACE784A1C960543A68C99684FC458F6C28D58CB31B26B562C194935D3949331A0AF4447C3E59DE23FE849F335A607FF86035B353A359652174276E28DD643E74AA8179EC99976F6B6CDC165C45B71E10D4E8A70BF3A3A7FB3BEBBBBBBDBF6F4EF7FC00B76C044AC);

-- ----------------------------
-- Table structure for player_skills
-- ----------------------------
DROP TABLE IF EXISTS `player_skills`;
CREATE TABLE `player_skills`  (
  `player_id` int(11) NOT NULL,
  `skillId` int(11) NOT NULL,
  `skillLevel` int(3) NOT NULL DEFAULT 1,
  PRIMARY KEY (`player_id`, `skillId`) USING BTREE,
  CONSTRAINT `player_skills_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_skills
-- ----------------------------
INSERT INTO `player_skills` VALUES (1224, 30001, 1);
INSERT INTO `player_skills` VALUES (1232, 40, 1);
INSERT INTO `player_skills` VALUES (1232, 100, 1);
INSERT INTO `player_skills` VALUES (1232, 103, 1);
INSERT INTO `player_skills` VALUES (1232, 106, 1);
INSERT INTO `player_skills` VALUES (1232, 107, 1);
INSERT INTO `player_skills` VALUES (1232, 111, 1);
INSERT INTO `player_skills` VALUES (1232, 135, 1);
INSERT INTO `player_skills` VALUES (1232, 137, 1);
INSERT INTO `player_skills` VALUES (1232, 153, 1);
INSERT INTO `player_skills` VALUES (1232, 154, 1);
INSERT INTO `player_skills` VALUES (1232, 155, 1);
INSERT INTO `player_skills` VALUES (1232, 156, 1);
INSERT INTO `player_skills` VALUES (1232, 218, 1);
INSERT INTO `player_skills` VALUES (1232, 219, 1);
INSERT INTO `player_skills` VALUES (1232, 220, 1);
INSERT INTO `player_skills` VALUES (1232, 221, 1);
INSERT INTO `player_skills` VALUES (1232, 222, 1);
INSERT INTO `player_skills` VALUES (1232, 224, 1);
INSERT INTO `player_skills` VALUES (1232, 226, 1);
INSERT INTO `player_skills` VALUES (1232, 228, 1);
INSERT INTO `player_skills` VALUES (1232, 232, 1);
INSERT INTO `player_skills` VALUES (1232, 233, 1);
INSERT INTO `player_skills` VALUES (1232, 234, 1);
INSERT INTO `player_skills` VALUES (1232, 235, 1);
INSERT INTO `player_skills` VALUES (1232, 236, 1);
INSERT INTO `player_skills` VALUES (1232, 237, 1);
INSERT INTO `player_skills` VALUES (1232, 243, 1);
INSERT INTO `player_skills` VALUES (1232, 245, 1);
INSERT INTO `player_skills` VALUES (1232, 246, 1);
INSERT INTO `player_skills` VALUES (1232, 247, 1);
INSERT INTO `player_skills` VALUES (1232, 249, 1);
INSERT INTO `player_skills` VALUES (1232, 250, 1);
INSERT INTO `player_skills` VALUES (1232, 251, 1);
INSERT INTO `player_skills` VALUES (1232, 252, 1);
INSERT INTO `player_skills` VALUES (1232, 253, 1);
INSERT INTO `player_skills` VALUES (1232, 254, 1);
INSERT INTO `player_skills` VALUES (1232, 283, 1);
INSERT INTO `player_skills` VALUES (1232, 297, 1);
INSERT INTO `player_skills` VALUES (1232, 298, 1);
INSERT INTO `player_skills` VALUES (1232, 302, 1);
INSERT INTO `player_skills` VALUES (1232, 308, 1);
INSERT INTO `player_skills` VALUES (1232, 309, 1);
INSERT INTO `player_skills` VALUES (1232, 310, 1);
INSERT INTO `player_skills` VALUES (1232, 311, 1);
INSERT INTO `player_skills` VALUES (1232, 312, 1);
INSERT INTO `player_skills` VALUES (1232, 313, 1);
INSERT INTO `player_skills` VALUES (1232, 314, 1);
INSERT INTO `player_skills` VALUES (1232, 315, 1);
INSERT INTO `player_skills` VALUES (1232, 316, 1);
INSERT INTO `player_skills` VALUES (1232, 317, 1);
INSERT INTO `player_skills` VALUES (1232, 318, 1);
INSERT INTO `player_skills` VALUES (1232, 319, 1);
INSERT INTO `player_skills` VALUES (1232, 320, 1);
INSERT INTO `player_skills` VALUES (1232, 321, 1);
INSERT INTO `player_skills` VALUES (1232, 352, 1);
INSERT INTO `player_skills` VALUES (1232, 353, 1);
INSERT INTO `player_skills` VALUES (1232, 356, 1);
INSERT INTO `player_skills` VALUES (1232, 1155, 1);
INSERT INTO `player_skills` VALUES (1232, 1156, 1);
INSERT INTO `player_skills` VALUES (1232, 1157, 1);
INSERT INTO `player_skills` VALUES (1232, 1158, 1);
INSERT INTO `player_skills` VALUES (1232, 1159, 1);
INSERT INTO `player_skills` VALUES (1232, 1160, 1);
INSERT INTO `player_skills` VALUES (1232, 1161, 1);
INSERT INTO `player_skills` VALUES (1232, 1162, 1);
INSERT INTO `player_skills` VALUES (1232, 1163, 1);
INSERT INTO `player_skills` VALUES (1232, 1164, 1);
INSERT INTO `player_skills` VALUES (1232, 1165, 1);
INSERT INTO `player_skills` VALUES (1232, 1166, 1);
INSERT INTO `player_skills` VALUES (1232, 1167, 1);
INSERT INTO `player_skills` VALUES (1232, 1168, 1);
INSERT INTO `player_skills` VALUES (1232, 1169, 1);
INSERT INTO `player_skills` VALUES (1232, 1170, 1);
INSERT INTO `player_skills` VALUES (1232, 1171, 1);
INSERT INTO `player_skills` VALUES (1232, 1172, 1);
INSERT INTO `player_skills` VALUES (1232, 1173, 1);
INSERT INTO `player_skills` VALUES (1232, 1174, 1);
INSERT INTO `player_skills` VALUES (1232, 1175, 1);
INSERT INTO `player_skills` VALUES (1232, 1176, 1);
INSERT INTO `player_skills` VALUES (1232, 1178, 1);
INSERT INTO `player_skills` VALUES (1232, 1179, 1);
INSERT INTO `player_skills` VALUES (1232, 1180, 1);
INSERT INTO `player_skills` VALUES (1232, 1181, 1);
INSERT INTO `player_skills` VALUES (1232, 1182, 1);
INSERT INTO `player_skills` VALUES (1232, 1183, 1);
INSERT INTO `player_skills` VALUES (1232, 1184, 1);
INSERT INTO `player_skills` VALUES (1232, 1185, 1);
INSERT INTO `player_skills` VALUES (1232, 1186, 1);
INSERT INTO `player_skills` VALUES (1232, 1187, 1);
INSERT INTO `player_skills` VALUES (1232, 1188, 1);
INSERT INTO `player_skills` VALUES (1232, 1189, 1);
INSERT INTO `player_skills` VALUES (1232, 1190, 1);
INSERT INTO `player_skills` VALUES (1232, 1191, 1);
INSERT INTO `player_skills` VALUES (1232, 1192, 1);
INSERT INTO `player_skills` VALUES (1232, 1193, 1);
INSERT INTO `player_skills` VALUES (1232, 1194, 1);
INSERT INTO `player_skills` VALUES (1232, 1195, 1);
INSERT INTO `player_skills` VALUES (1232, 1196, 1);
INSERT INTO `player_skills` VALUES (1232, 1197, 1);
INSERT INTO `player_skills` VALUES (1232, 1198, 1);
INSERT INTO `player_skills` VALUES (1232, 1199, 1);
INSERT INTO `player_skills` VALUES (1232, 1200, 1);
INSERT INTO `player_skills` VALUES (1232, 1201, 1);
INSERT INTO `player_skills` VALUES (1232, 1202, 1);
INSERT INTO `player_skills` VALUES (1232, 1203, 1);
INSERT INTO `player_skills` VALUES (1232, 1204, 1);
INSERT INTO `player_skills` VALUES (1232, 1205, 1);
INSERT INTO `player_skills` VALUES (1232, 1206, 1);
INSERT INTO `player_skills` VALUES (1232, 1207, 1);
INSERT INTO `player_skills` VALUES (1232, 1208, 1);
INSERT INTO `player_skills` VALUES (1232, 1209, 1);
INSERT INTO `player_skills` VALUES (1232, 1217, 1);
INSERT INTO `player_skills` VALUES (1232, 1218, 1);
INSERT INTO `player_skills` VALUES (1232, 1219, 1);
INSERT INTO `player_skills` VALUES (1232, 1220, 1);
INSERT INTO `player_skills` VALUES (1232, 1221, 1);
INSERT INTO `player_skills` VALUES (1232, 1222, 1);
INSERT INTO `player_skills` VALUES (1232, 1223, 1);
INSERT INTO `player_skills` VALUES (1232, 1224, 1);
INSERT INTO `player_skills` VALUES (1232, 1225, 1);
INSERT INTO `player_skills` VALUES (1232, 1226, 1);
INSERT INTO `player_skills` VALUES (1232, 1227, 1);
INSERT INTO `player_skills` VALUES (1232, 1228, 1);
INSERT INTO `player_skills` VALUES (1232, 1229, 1);
INSERT INTO `player_skills` VALUES (1232, 1230, 1);
INSERT INTO `player_skills` VALUES (1232, 1231, 1);
INSERT INTO `player_skills` VALUES (1232, 1232, 1);
INSERT INTO `player_skills` VALUES (1232, 1233, 1);
INSERT INTO `player_skills` VALUES (1232, 1234, 1);
INSERT INTO `player_skills` VALUES (1232, 1235, 1);
INSERT INTO `player_skills` VALUES (1232, 1236, 1);
INSERT INTO `player_skills` VALUES (1232, 1237, 1);
INSERT INTO `player_skills` VALUES (1232, 1238, 1);
INSERT INTO `player_skills` VALUES (1232, 1239, 1);
INSERT INTO `player_skills` VALUES (1232, 1240, 1);
INSERT INTO `player_skills` VALUES (1232, 1241, 1);
INSERT INTO `player_skills` VALUES (1232, 1242, 1);
INSERT INTO `player_skills` VALUES (1232, 1243, 1);
INSERT INTO `player_skills` VALUES (1232, 1244, 1);
INSERT INTO `player_skills` VALUES (1232, 1245, 1);
INSERT INTO `player_skills` VALUES (1232, 1246, 1);
INSERT INTO `player_skills` VALUES (1232, 1247, 1);
INSERT INTO `player_skills` VALUES (1232, 1248, 1);
INSERT INTO `player_skills` VALUES (1232, 1249, 1);
INSERT INTO `player_skills` VALUES (1232, 1250, 1);
INSERT INTO `player_skills` VALUES (1232, 1251, 1);
INSERT INTO `player_skills` VALUES (1232, 1252, 1);
INSERT INTO `player_skills` VALUES (1232, 1253, 1);
INSERT INTO `player_skills` VALUES (1232, 1254, 1);
INSERT INTO `player_skills` VALUES (1232, 1255, 1);
INSERT INTO `player_skills` VALUES (1232, 1256, 1);
INSERT INTO `player_skills` VALUES (1232, 1257, 1);
INSERT INTO `player_skills` VALUES (1232, 1258, 1);
INSERT INTO `player_skills` VALUES (1232, 1259, 1);
INSERT INTO `player_skills` VALUES (1232, 1260, 1);
INSERT INTO `player_skills` VALUES (1232, 1261, 1);
INSERT INTO `player_skills` VALUES (1232, 1262, 1);
INSERT INTO `player_skills` VALUES (1232, 1263, 1);
INSERT INTO `player_skills` VALUES (1232, 1264, 1);
INSERT INTO `player_skills` VALUES (1232, 1265, 1);
INSERT INTO `player_skills` VALUES (1232, 1266, 1);
INSERT INTO `player_skills` VALUES (1232, 1267, 1);
INSERT INTO `player_skills` VALUES (1232, 1268, 1);
INSERT INTO `player_skills` VALUES (1232, 1269, 1);
INSERT INTO `player_skills` VALUES (1232, 1270, 1);
INSERT INTO `player_skills` VALUES (1232, 1271, 1);
INSERT INTO `player_skills` VALUES (1232, 1272, 1);
INSERT INTO `player_skills` VALUES (1232, 1273, 1);
INSERT INTO `player_skills` VALUES (1232, 1274, 1);
INSERT INTO `player_skills` VALUES (1232, 1275, 1);
INSERT INTO `player_skills` VALUES (1232, 1276, 1);
INSERT INTO `player_skills` VALUES (1232, 1277, 1);
INSERT INTO `player_skills` VALUES (1232, 1278, 1);
INSERT INTO `player_skills` VALUES (1232, 1279, 1);
INSERT INTO `player_skills` VALUES (1232, 1280, 1);
INSERT INTO `player_skills` VALUES (1232, 1281, 1);
INSERT INTO `player_skills` VALUES (1232, 1282, 1);
INSERT INTO `player_skills` VALUES (1232, 1284, 1);
INSERT INTO `player_skills` VALUES (1232, 1285, 1);
INSERT INTO `player_skills` VALUES (1232, 1286, 1);
INSERT INTO `player_skills` VALUES (1232, 1287, 1);
INSERT INTO `player_skills` VALUES (1232, 1288, 1);
INSERT INTO `player_skills` VALUES (1232, 1289, 1);
INSERT INTO `player_skills` VALUES (1232, 1290, 1);
INSERT INTO `player_skills` VALUES (1232, 1291, 1);
INSERT INTO `player_skills` VALUES (1232, 1292, 1);
INSERT INTO `player_skills` VALUES (1232, 1293, 1);
INSERT INTO `player_skills` VALUES (1232, 1294, 1);
INSERT INTO `player_skills` VALUES (1232, 1296, 1);
INSERT INTO `player_skills` VALUES (1232, 1297, 1);
INSERT INTO `player_skills` VALUES (1232, 1298, 1);
INSERT INTO `player_skills` VALUES (1232, 1299, 1);
INSERT INTO `player_skills` VALUES (1232, 1300, 1);
INSERT INTO `player_skills` VALUES (1232, 1301, 1);
INSERT INTO `player_skills` VALUES (1232, 1302, 1);
INSERT INTO `player_skills` VALUES (1232, 1303, 1);
INSERT INTO `player_skills` VALUES (1232, 1304, 1);
INSERT INTO `player_skills` VALUES (1232, 1328, 1);
INSERT INTO `player_skills` VALUES (1232, 1338, 1);
INSERT INTO `player_skills` VALUES (1232, 1343, 1);
INSERT INTO `player_skills` VALUES (1232, 1346, 1);
INSERT INTO `player_skills` VALUES (1232, 1347, 1);
INSERT INTO `player_skills` VALUES (1232, 1349, 1);
INSERT INTO `player_skills` VALUES (1232, 1357, 1);
INSERT INTO `player_skills` VALUES (1232, 1358, 1);
INSERT INTO `player_skills` VALUES (1232, 1359, 1);
INSERT INTO `player_skills` VALUES (1232, 1360, 1);
INSERT INTO `player_skills` VALUES (1232, 1361, 1);
INSERT INTO `player_skills` VALUES (1232, 1362, 1);
INSERT INTO `player_skills` VALUES (1232, 1363, 1);
INSERT INTO `player_skills` VALUES (1232, 1365, 1);
INSERT INTO `player_skills` VALUES (1232, 1366, 1);
INSERT INTO `player_skills` VALUES (1232, 1367, 1);
INSERT INTO `player_skills` VALUES (1232, 1368, 1);
INSERT INTO `player_skills` VALUES (1232, 1369, 1);
INSERT INTO `player_skills` VALUES (1232, 1370, 1);
INSERT INTO `player_skills` VALUES (1232, 1371, 1);
INSERT INTO `player_skills` VALUES (1232, 1372, 1);
INSERT INTO `player_skills` VALUES (1232, 1373, 1);
INSERT INTO `player_skills` VALUES (1232, 1374, 1);
INSERT INTO `player_skills` VALUES (1232, 1375, 1);
INSERT INTO `player_skills` VALUES (1232, 1384, 1);
INSERT INTO `player_skills` VALUES (1232, 1385, 1);
INSERT INTO `player_skills` VALUES (1232, 1386, 1);
INSERT INTO `player_skills` VALUES (1232, 1387, 1);
INSERT INTO `player_skills` VALUES (1232, 1388, 1);
INSERT INTO `player_skills` VALUES (1232, 1389, 1);
INSERT INTO `player_skills` VALUES (1232, 1390, 1);
INSERT INTO `player_skills` VALUES (1232, 1391, 1);
INSERT INTO `player_skills` VALUES (1232, 1392, 1);
INSERT INTO `player_skills` VALUES (1232, 1393, 1);
INSERT INTO `player_skills` VALUES (1232, 1394, 1);
INSERT INTO `player_skills` VALUES (1232, 1395, 1);
INSERT INTO `player_skills` VALUES (1232, 1396, 1);
INSERT INTO `player_skills` VALUES (1232, 1397, 1);
INSERT INTO `player_skills` VALUES (1232, 1398, 1);
INSERT INTO `player_skills` VALUES (1232, 1399, 1);
INSERT INTO `player_skills` VALUES (1232, 1400, 1);
INSERT INTO `player_skills` VALUES (1232, 1401, 1);
INSERT INTO `player_skills` VALUES (1232, 1403, 1);
INSERT INTO `player_skills` VALUES (1232, 1404, 1);
INSERT INTO `player_skills` VALUES (1232, 1405, 1);
INSERT INTO `player_skills` VALUES (1232, 1406, 1);
INSERT INTO `player_skills` VALUES (1232, 1407, 1);
INSERT INTO `player_skills` VALUES (1232, 1408, 1);
INSERT INTO `player_skills` VALUES (1232, 1409, 1);
INSERT INTO `player_skills` VALUES (1232, 1410, 1);
INSERT INTO `player_skills` VALUES (1232, 1411, 1);
INSERT INTO `player_skills` VALUES (1232, 1412, 1);
INSERT INTO `player_skills` VALUES (1232, 1413, 1);
INSERT INTO `player_skills` VALUES (1232, 1414, 1);
INSERT INTO `player_skills` VALUES (1232, 1415, 1);
INSERT INTO `player_skills` VALUES (1232, 1416, 1);
INSERT INTO `player_skills` VALUES (1232, 1417, 1);
INSERT INTO `player_skills` VALUES (1232, 1421, 1);
INSERT INTO `player_skills` VALUES (1232, 1422, 1);
INSERT INTO `player_skills` VALUES (1232, 1423, 1);
INSERT INTO `player_skills` VALUES (1232, 1424, 1);
INSERT INTO `player_skills` VALUES (1232, 1425, 1);
INSERT INTO `player_skills` VALUES (1232, 1426, 1);
INSERT INTO `player_skills` VALUES (1232, 1427, 1);
INSERT INTO `player_skills` VALUES (1232, 1428, 1);
INSERT INTO `player_skills` VALUES (1232, 1429, 1);
INSERT INTO `player_skills` VALUES (1232, 1430, 1);
INSERT INTO `player_skills` VALUES (1232, 1432, 1);
INSERT INTO `player_skills` VALUES (1232, 1433, 1);
INSERT INTO `player_skills` VALUES (1232, 1434, 1);
INSERT INTO `player_skills` VALUES (1232, 1435, 1);
INSERT INTO `player_skills` VALUES (1232, 1436, 1);
INSERT INTO `player_skills` VALUES (1232, 1437, 1);
INSERT INTO `player_skills` VALUES (1232, 1438, 1);
INSERT INTO `player_skills` VALUES (1232, 1439, 1);
INSERT INTO `player_skills` VALUES (1232, 1440, 1);
INSERT INTO `player_skills` VALUES (1232, 1441, 1);
INSERT INTO `player_skills` VALUES (1232, 1442, 1);
INSERT INTO `player_skills` VALUES (1232, 1443, 1);
INSERT INTO `player_skills` VALUES (1232, 1444, 1);
INSERT INTO `player_skills` VALUES (1232, 1445, 1);
INSERT INTO `player_skills` VALUES (1232, 1446, 1);
INSERT INTO `player_skills` VALUES (1232, 1447, 1);
INSERT INTO `player_skills` VALUES (1232, 1476, 1);
INSERT INTO `player_skills` VALUES (1232, 1477, 1);
INSERT INTO `player_skills` VALUES (1232, 1478, 1);
INSERT INTO `player_skills` VALUES (1232, 1479, 1);
INSERT INTO `player_skills` VALUES (1232, 1480, 1);
INSERT INTO `player_skills` VALUES (1232, 1481, 1);
INSERT INTO `player_skills` VALUES (1232, 1482, 1);
INSERT INTO `player_skills` VALUES (1232, 1483, 1);
INSERT INTO `player_skills` VALUES (1232, 1484, 1);
INSERT INTO `player_skills` VALUES (1232, 1485, 1);
INSERT INTO `player_skills` VALUES (1232, 1494, 1);
INSERT INTO `player_skills` VALUES (1232, 1495, 1);
INSERT INTO `player_skills` VALUES (1232, 1496, 1);
INSERT INTO `player_skills` VALUES (1232, 1497, 1);
INSERT INTO `player_skills` VALUES (1232, 1498, 1);
INSERT INTO `player_skills` VALUES (1232, 1499, 1);
INSERT INTO `player_skills` VALUES (1232, 1500, 1);
INSERT INTO `player_skills` VALUES (1232, 1501, 1);
INSERT INTO `player_skills` VALUES (1232, 1502, 1);
INSERT INTO `player_skills` VALUES (1232, 1503, 1);
INSERT INTO `player_skills` VALUES (1232, 1504, 1);
INSERT INTO `player_skills` VALUES (1232, 1505, 1);
INSERT INTO `player_skills` VALUES (1232, 1506, 1);
INSERT INTO `player_skills` VALUES (1232, 1507, 1);
INSERT INTO `player_skills` VALUES (1232, 1508, 1);
INSERT INTO `player_skills` VALUES (1232, 1509, 1);
INSERT INTO `player_skills` VALUES (1232, 1510, 1);
INSERT INTO `player_skills` VALUES (1232, 1511, 1);
INSERT INTO `player_skills` VALUES (1232, 1512, 1);
INSERT INTO `player_skills` VALUES (1232, 1513, 1);
INSERT INTO `player_skills` VALUES (1232, 1514, 1);
INSERT INTO `player_skills` VALUES (1232, 1515, 1);
INSERT INTO `player_skills` VALUES (1232, 1516, 1);
INSERT INTO `player_skills` VALUES (1232, 1517, 1);
INSERT INTO `player_skills` VALUES (1232, 1518, 1);
INSERT INTO `player_skills` VALUES (1232, 1519, 1);
INSERT INTO `player_skills` VALUES (1232, 1526, 1);
INSERT INTO `player_skills` VALUES (1232, 1527, 1);
INSERT INTO `player_skills` VALUES (1232, 1528, 1);
INSERT INTO `player_skills` VALUES (1232, 1529, 1);
INSERT INTO `player_skills` VALUES (1232, 1530, 1);
INSERT INTO `player_skills` VALUES (1232, 1531, 1);
INSERT INTO `player_skills` VALUES (1232, 1532, 1);
INSERT INTO `player_skills` VALUES (1232, 1533, 1);
INSERT INTO `player_skills` VALUES (1232, 1534, 1);
INSERT INTO `player_skills` VALUES (1232, 1535, 1);
INSERT INTO `player_skills` VALUES (1232, 1536, 1);
INSERT INTO `player_skills` VALUES (1232, 1537, 1);
INSERT INTO `player_skills` VALUES (1232, 1538, 1);
INSERT INTO `player_skills` VALUES (1232, 1539, 1);
INSERT INTO `player_skills` VALUES (1232, 1543, 1);
INSERT INTO `player_skills` VALUES (1232, 1544, 1);
INSERT INTO `player_skills` VALUES (1232, 1545, 1);
INSERT INTO `player_skills` VALUES (1232, 1546, 1);
INSERT INTO `player_skills` VALUES (1232, 1547, 1);
INSERT INTO `player_skills` VALUES (1232, 1548, 1);
INSERT INTO `player_skills` VALUES (1232, 1549, 1);
INSERT INTO `player_skills` VALUES (1232, 4611, 1);
INSERT INTO `player_skills` VALUES (1232, 4612, 1);
INSERT INTO `player_skills` VALUES (1232, 4613, 1);
INSERT INTO `player_skills` VALUES (1232, 4659, 1);
INSERT INTO `player_skills` VALUES (1232, 4660, 1);
INSERT INTO `player_skills` VALUES (1232, 30002, 1);
INSERT INTO `player_skills` VALUES (1232, 30003, 1);
INSERT INTO `player_skills` VALUES (1232, 40009, 1);
INSERT INTO `player_skills` VALUES (1300, 37, 1);
INSERT INTO `player_skills` VALUES (1300, 40, 1);
INSERT INTO `player_skills` VALUES (1300, 41, 1);
INSERT INTO `player_skills` VALUES (1300, 44, 1);
INSERT INTO `player_skills` VALUES (1300, 45, 1);
INSERT INTO `player_skills` VALUES (1300, 48, 1);
INSERT INTO `player_skills` VALUES (1300, 53, 1);
INSERT INTO `player_skills` VALUES (1300, 55, 1);
INSERT INTO `player_skills` VALUES (1300, 66, 1);
INSERT INTO `player_skills` VALUES (1300, 103, 1);
INSERT INTO `player_skills` VALUES (1300, 130, 1);
INSERT INTO `player_skills` VALUES (1300, 131, 1);
INSERT INTO `player_skills` VALUES (1300, 134, 1);
INSERT INTO `player_skills` VALUES (1300, 143, 1);
INSERT INTO `player_skills` VALUES (1300, 144, 1);
INSERT INTO `player_skills` VALUES (1300, 145, 1);
INSERT INTO `player_skills` VALUES (1300, 146, 1);
INSERT INTO `player_skills` VALUES (1300, 147, 1);
INSERT INTO `player_skills` VALUES (1300, 148, 1);
INSERT INTO `player_skills` VALUES (1300, 149, 1);
INSERT INTO `player_skills` VALUES (1300, 150, 1);
INSERT INTO `player_skills` VALUES (1300, 151, 1);
INSERT INTO `player_skills` VALUES (1300, 157, 1);
INSERT INTO `player_skills` VALUES (1300, 158, 1);
INSERT INTO `player_skills` VALUES (1300, 162, 1);
INSERT INTO `player_skills` VALUES (1300, 163, 1);
INSERT INTO `player_skills` VALUES (1300, 164, 1);
INSERT INTO `player_skills` VALUES (1300, 168, 1);
INSERT INTO `player_skills` VALUES (1300, 171, 1);
INSERT INTO `player_skills` VALUES (1300, 174, 1);
INSERT INTO `player_skills` VALUES (1300, 175, 1);
INSERT INTO `player_skills` VALUES (1300, 179, 1);
INSERT INTO `player_skills` VALUES (1300, 205, 1);
INSERT INTO `player_skills` VALUES (1300, 206, 1);
INSERT INTO `player_skills` VALUES (1300, 207, 1);
INSERT INTO `player_skills` VALUES (1300, 230, 1);
INSERT INTO `player_skills` VALUES (1300, 231, 1);
INSERT INTO `player_skills` VALUES (1300, 243, 1);
INSERT INTO `player_skills` VALUES (1300, 245, 1);
INSERT INTO `player_skills` VALUES (1300, 246, 1);
INSERT INTO `player_skills` VALUES (1300, 247, 1);
INSERT INTO `player_skills` VALUES (1300, 249, 1);
INSERT INTO `player_skills` VALUES (1300, 250, 1);
INSERT INTO `player_skills` VALUES (1300, 251, 1);
INSERT INTO `player_skills` VALUES (1300, 252, 1);
INSERT INTO `player_skills` VALUES (1300, 253, 1);
INSERT INTO `player_skills` VALUES (1300, 254, 1);
INSERT INTO `player_skills` VALUES (1300, 283, 1);
INSERT INTO `player_skills` VALUES (1300, 297, 1);
INSERT INTO `player_skills` VALUES (1300, 298, 1);
INSERT INTO `player_skills` VALUES (1300, 302, 1);
INSERT INTO `player_skills` VALUES (1300, 308, 1);
INSERT INTO `player_skills` VALUES (1300, 309, 1);
INSERT INTO `player_skills` VALUES (1300, 310, 1);
INSERT INTO `player_skills` VALUES (1300, 311, 1);
INSERT INTO `player_skills` VALUES (1300, 312, 1);
INSERT INTO `player_skills` VALUES (1300, 313, 1);
INSERT INTO `player_skills` VALUES (1300, 314, 1);
INSERT INTO `player_skills` VALUES (1300, 315, 1);
INSERT INTO `player_skills` VALUES (1300, 316, 1);
INSERT INTO `player_skills` VALUES (1300, 317, 1);
INSERT INTO `player_skills` VALUES (1300, 318, 1);
INSERT INTO `player_skills` VALUES (1300, 319, 1);
INSERT INTO `player_skills` VALUES (1300, 320, 1);
INSERT INTO `player_skills` VALUES (1300, 321, 1);
INSERT INTO `player_skills` VALUES (1300, 372, 1);
INSERT INTO `player_skills` VALUES (1300, 373, 1);
INSERT INTO `player_skills` VALUES (1300, 3182, 1);
INSERT INTO `player_skills` VALUES (1300, 3183, 1);
INSERT INTO `player_skills` VALUES (1300, 3184, 1);
INSERT INTO `player_skills` VALUES (1300, 3185, 1);
INSERT INTO `player_skills` VALUES (1300, 3186, 1);
INSERT INTO `player_skills` VALUES (1300, 3187, 1);
INSERT INTO `player_skills` VALUES (1300, 3188, 1);
INSERT INTO `player_skills` VALUES (1300, 3189, 1);
INSERT INTO `player_skills` VALUES (1300, 3190, 1);
INSERT INTO `player_skills` VALUES (1300, 3191, 1);
INSERT INTO `player_skills` VALUES (1300, 3192, 1);
INSERT INTO `player_skills` VALUES (1300, 3193, 1);
INSERT INTO `player_skills` VALUES (1300, 3194, 1);
INSERT INTO `player_skills` VALUES (1300, 3195, 1);
INSERT INTO `player_skills` VALUES (1300, 3196, 1);
INSERT INTO `player_skills` VALUES (1300, 3197, 1);
INSERT INTO `player_skills` VALUES (1300, 3198, 1);
INSERT INTO `player_skills` VALUES (1300, 3199, 1);
INSERT INTO `player_skills` VALUES (1300, 3200, 1);
INSERT INTO `player_skills` VALUES (1300, 3201, 1);
INSERT INTO `player_skills` VALUES (1300, 3202, 1);
INSERT INTO `player_skills` VALUES (1300, 3203, 1);
INSERT INTO `player_skills` VALUES (1300, 3204, 1);
INSERT INTO `player_skills` VALUES (1300, 3205, 1);
INSERT INTO `player_skills` VALUES (1300, 3206, 1);
INSERT INTO `player_skills` VALUES (1300, 3207, 1);
INSERT INTO `player_skills` VALUES (1300, 3208, 1);
INSERT INTO `player_skills` VALUES (1300, 3209, 1);
INSERT INTO `player_skills` VALUES (1300, 3210, 1);
INSERT INTO `player_skills` VALUES (1300, 3211, 1);
INSERT INTO `player_skills` VALUES (1300, 3212, 1);
INSERT INTO `player_skills` VALUES (1300, 3213, 1);
INSERT INTO `player_skills` VALUES (1300, 3214, 1);
INSERT INTO `player_skills` VALUES (1300, 3215, 1);
INSERT INTO `player_skills` VALUES (1300, 3216, 1);
INSERT INTO `player_skills` VALUES (1300, 3217, 1);
INSERT INTO `player_skills` VALUES (1300, 3218, 1);
INSERT INTO `player_skills` VALUES (1300, 3219, 1);
INSERT INTO `player_skills` VALUES (1300, 3220, 1);
INSERT INTO `player_skills` VALUES (1300, 3221, 1);
INSERT INTO `player_skills` VALUES (1300, 3222, 1);
INSERT INTO `player_skills` VALUES (1300, 3223, 1);
INSERT INTO `player_skills` VALUES (1300, 3224, 1);
INSERT INTO `player_skills` VALUES (1300, 3225, 1);
INSERT INTO `player_skills` VALUES (1300, 3226, 1);
INSERT INTO `player_skills` VALUES (1300, 3227, 1);
INSERT INTO `player_skills` VALUES (1300, 3228, 1);
INSERT INTO `player_skills` VALUES (1300, 3229, 1);
INSERT INTO `player_skills` VALUES (1300, 3230, 1);
INSERT INTO `player_skills` VALUES (1300, 3231, 1);
INSERT INTO `player_skills` VALUES (1300, 3232, 1);
INSERT INTO `player_skills` VALUES (1300, 3233, 1);
INSERT INTO `player_skills` VALUES (1300, 3234, 1);
INSERT INTO `player_skills` VALUES (1300, 3235, 1);
INSERT INTO `player_skills` VALUES (1300, 3270, 1);
INSERT INTO `player_skills` VALUES (1300, 3271, 1);
INSERT INTO `player_skills` VALUES (1300, 3272, 1);
INSERT INTO `player_skills` VALUES (1300, 3273, 1);
INSERT INTO `player_skills` VALUES (1300, 3274, 1);
INSERT INTO `player_skills` VALUES (1300, 3275, 1);
INSERT INTO `player_skills` VALUES (1300, 3276, 1);
INSERT INTO `player_skills` VALUES (1300, 3277, 1);
INSERT INTO `player_skills` VALUES (1300, 3278, 1);
INSERT INTO `player_skills` VALUES (1300, 3279, 1);
INSERT INTO `player_skills` VALUES (1300, 3280, 1);
INSERT INTO `player_skills` VALUES (1300, 3281, 1);
INSERT INTO `player_skills` VALUES (1300, 3282, 1);
INSERT INTO `player_skills` VALUES (1300, 3283, 1);
INSERT INTO `player_skills` VALUES (1300, 3284, 1);
INSERT INTO `player_skills` VALUES (1300, 3285, 1);
INSERT INTO `player_skills` VALUES (1300, 3286, 1);
INSERT INTO `player_skills` VALUES (1300, 3287, 1);
INSERT INTO `player_skills` VALUES (1300, 3288, 1);
INSERT INTO `player_skills` VALUES (1300, 3289, 1);
INSERT INTO `player_skills` VALUES (1300, 3290, 1);
INSERT INTO `player_skills` VALUES (1300, 3291, 1);
INSERT INTO `player_skills` VALUES (1300, 3292, 1);
INSERT INTO `player_skills` VALUES (1300, 3293, 1);
INSERT INTO `player_skills` VALUES (1300, 3294, 1);
INSERT INTO `player_skills` VALUES (1300, 3295, 1);
INSERT INTO `player_skills` VALUES (1300, 3296, 1);
INSERT INTO `player_skills` VALUES (1300, 3297, 1);
INSERT INTO `player_skills` VALUES (1300, 3298, 1);
INSERT INTO `player_skills` VALUES (1300, 3299, 1);
INSERT INTO `player_skills` VALUES (1300, 3300, 1);
INSERT INTO `player_skills` VALUES (1300, 3301, 1);
INSERT INTO `player_skills` VALUES (1300, 3302, 1);
INSERT INTO `player_skills` VALUES (1300, 3303, 1);
INSERT INTO `player_skills` VALUES (1300, 3304, 1);
INSERT INTO `player_skills` VALUES (1300, 3305, 1);
INSERT INTO `player_skills` VALUES (1300, 3306, 1);
INSERT INTO `player_skills` VALUES (1300, 3307, 1);
INSERT INTO `player_skills` VALUES (1300, 3308, 1);
INSERT INTO `player_skills` VALUES (1300, 3309, 1);
INSERT INTO `player_skills` VALUES (1300, 3310, 1);
INSERT INTO `player_skills` VALUES (1300, 3311, 1);
INSERT INTO `player_skills` VALUES (1300, 3328, 1);
INSERT INTO `player_skills` VALUES (1300, 3331, 1);
INSERT INTO `player_skills` VALUES (1300, 3338, 1);
INSERT INTO `player_skills` VALUES (1300, 3339, 1);
INSERT INTO `player_skills` VALUES (1300, 3340, 1);
INSERT INTO `player_skills` VALUES (1300, 3341, 1);
INSERT INTO `player_skills` VALUES (1300, 3342, 1);
INSERT INTO `player_skills` VALUES (1300, 3343, 1);
INSERT INTO `player_skills` VALUES (1300, 3344, 1);
INSERT INTO `player_skills` VALUES (1300, 3345, 1);
INSERT INTO `player_skills` VALUES (1300, 3346, 1);
INSERT INTO `player_skills` VALUES (1300, 3347, 1);
INSERT INTO `player_skills` VALUES (1300, 3348, 1);
INSERT INTO `player_skills` VALUES (1300, 3349, 1);
INSERT INTO `player_skills` VALUES (1300, 3350, 1);
INSERT INTO `player_skills` VALUES (1300, 3351, 1);
INSERT INTO `player_skills` VALUES (1300, 3352, 1);
INSERT INTO `player_skills` VALUES (1300, 3353, 1);
INSERT INTO `player_skills` VALUES (1300, 3354, 1);
INSERT INTO `player_skills` VALUES (1300, 3355, 1);
INSERT INTO `player_skills` VALUES (1300, 3356, 1);
INSERT INTO `player_skills` VALUES (1300, 3357, 1);
INSERT INTO `player_skills` VALUES (1300, 3358, 1);
INSERT INTO `player_skills` VALUES (1300, 3359, 1);
INSERT INTO `player_skills` VALUES (1300, 3360, 1);
INSERT INTO `player_skills` VALUES (1300, 3361, 1);
INSERT INTO `player_skills` VALUES (1300, 3362, 1);
INSERT INTO `player_skills` VALUES (1300, 3363, 1);
INSERT INTO `player_skills` VALUES (1300, 3364, 1);
INSERT INTO `player_skills` VALUES (1300, 3365, 1);
INSERT INTO `player_skills` VALUES (1300, 3366, 1);
INSERT INTO `player_skills` VALUES (1300, 3367, 1);
INSERT INTO `player_skills` VALUES (1300, 3368, 1);
INSERT INTO `player_skills` VALUES (1300, 3369, 1);
INSERT INTO `player_skills` VALUES (1300, 3370, 1);
INSERT INTO `player_skills` VALUES (1300, 3372, 1);
INSERT INTO `player_skills` VALUES (1300, 3373, 1);
INSERT INTO `player_skills` VALUES (1300, 3374, 1);
INSERT INTO `player_skills` VALUES (1300, 3375, 1);
INSERT INTO `player_skills` VALUES (1300, 3376, 1);
INSERT INTO `player_skills` VALUES (1300, 3377, 1);
INSERT INTO `player_skills` VALUES (1300, 3378, 1);
INSERT INTO `player_skills` VALUES (1300, 3379, 1);
INSERT INTO `player_skills` VALUES (1300, 3380, 1);
INSERT INTO `player_skills` VALUES (1300, 3381, 1);
INSERT INTO `player_skills` VALUES (1300, 3382, 1);
INSERT INTO `player_skills` VALUES (1300, 3383, 1);
INSERT INTO `player_skills` VALUES (1300, 3384, 1);
INSERT INTO `player_skills` VALUES (1300, 3385, 1);
INSERT INTO `player_skills` VALUES (1300, 3386, 1);
INSERT INTO `player_skills` VALUES (1300, 3387, 1);
INSERT INTO `player_skills` VALUES (1300, 3388, 1);
INSERT INTO `player_skills` VALUES (1300, 3389, 1);
INSERT INTO `player_skills` VALUES (1300, 3390, 1);
INSERT INTO `player_skills` VALUES (1300, 3391, 1);
INSERT INTO `player_skills` VALUES (1300, 3392, 1);
INSERT INTO `player_skills` VALUES (1300, 3393, 1);
INSERT INTO `player_skills` VALUES (1300, 3394, 1);
INSERT INTO `player_skills` VALUES (1300, 3395, 1);
INSERT INTO `player_skills` VALUES (1300, 3404, 1);
INSERT INTO `player_skills` VALUES (1300, 3405, 1);
INSERT INTO `player_skills` VALUES (1300, 3406, 1);
INSERT INTO `player_skills` VALUES (1300, 3407, 1);
INSERT INTO `player_skills` VALUES (1300, 3408, 1);
INSERT INTO `player_skills` VALUES (1300, 3409, 1);
INSERT INTO `player_skills` VALUES (1300, 3410, 1);
INSERT INTO `player_skills` VALUES (1300, 3411, 1);
INSERT INTO `player_skills` VALUES (1300, 3412, 1);
INSERT INTO `player_skills` VALUES (1300, 3413, 1);
INSERT INTO `player_skills` VALUES (1300, 3414, 1);
INSERT INTO `player_skills` VALUES (1300, 3415, 1);
INSERT INTO `player_skills` VALUES (1300, 3416, 1);
INSERT INTO `player_skills` VALUES (1300, 3417, 1);
INSERT INTO `player_skills` VALUES (1300, 3418, 1);
INSERT INTO `player_skills` VALUES (1300, 3419, 1);
INSERT INTO `player_skills` VALUES (1300, 3420, 1);
INSERT INTO `player_skills` VALUES (1300, 3421, 1);
INSERT INTO `player_skills` VALUES (1300, 3422, 1);
INSERT INTO `player_skills` VALUES (1300, 3423, 1);
INSERT INTO `player_skills` VALUES (1300, 3424, 1);
INSERT INTO `player_skills` VALUES (1300, 3425, 1);
INSERT INTO `player_skills` VALUES (1300, 3426, 1);
INSERT INTO `player_skills` VALUES (1300, 3427, 1);
INSERT INTO `player_skills` VALUES (1300, 3428, 1);
INSERT INTO `player_skills` VALUES (1300, 3429, 1);
INSERT INTO `player_skills` VALUES (1300, 3430, 1);
INSERT INTO `player_skills` VALUES (1300, 3431, 1);
INSERT INTO `player_skills` VALUES (1300, 3432, 1);
INSERT INTO `player_skills` VALUES (1300, 3433, 1);
INSERT INTO `player_skills` VALUES (1300, 3434, 1);
INSERT INTO `player_skills` VALUES (1300, 3435, 1);
INSERT INTO `player_skills` VALUES (1300, 3436, 1);
INSERT INTO `player_skills` VALUES (1300, 3437, 1);
INSERT INTO `player_skills` VALUES (1300, 3438, 1);
INSERT INTO `player_skills` VALUES (1300, 3439, 1);
INSERT INTO `player_skills` VALUES (1300, 3440, 1);
INSERT INTO `player_skills` VALUES (1300, 3441, 1);
INSERT INTO `player_skills` VALUES (1300, 3442, 1);
INSERT INTO `player_skills` VALUES (1300, 3443, 1);
INSERT INTO `player_skills` VALUES (1300, 3444, 1);
INSERT INTO `player_skills` VALUES (1300, 3455, 1);
INSERT INTO `player_skills` VALUES (1300, 3456, 1);
INSERT INTO `player_skills` VALUES (1300, 3457, 1);
INSERT INTO `player_skills` VALUES (1300, 3458, 1);
INSERT INTO `player_skills` VALUES (1300, 3459, 1);
INSERT INTO `player_skills` VALUES (1300, 3460, 1);
INSERT INTO `player_skills` VALUES (1300, 3461, 1);
INSERT INTO `player_skills` VALUES (1300, 3462, 1);
INSERT INTO `player_skills` VALUES (1300, 3463, 1);
INSERT INTO `player_skills` VALUES (1300, 3464, 1);
INSERT INTO `player_skills` VALUES (1300, 3465, 1);
INSERT INTO `player_skills` VALUES (1300, 3466, 1);
INSERT INTO `player_skills` VALUES (1300, 3467, 1);
INSERT INTO `player_skills` VALUES (1300, 3468, 1);
INSERT INTO `player_skills` VALUES (1300, 3469, 1);
INSERT INTO `player_skills` VALUES (1300, 3470, 1);
INSERT INTO `player_skills` VALUES (1300, 3471, 1);
INSERT INTO `player_skills` VALUES (1300, 3472, 1);
INSERT INTO `player_skills` VALUES (1300, 3473, 1);
INSERT INTO `player_skills` VALUES (1300, 3474, 1);
INSERT INTO `player_skills` VALUES (1300, 3475, 1);
INSERT INTO `player_skills` VALUES (1300, 3476, 1);
INSERT INTO `player_skills` VALUES (1300, 3477, 1);
INSERT INTO `player_skills` VALUES (1300, 3478, 1);
INSERT INTO `player_skills` VALUES (1300, 3479, 1);
INSERT INTO `player_skills` VALUES (1300, 3481, 1);
INSERT INTO `player_skills` VALUES (1300, 3482, 1);
INSERT INTO `player_skills` VALUES (1300, 3483, 1);
INSERT INTO `player_skills` VALUES (1300, 3484, 1);
INSERT INTO `player_skills` VALUES (1300, 3485, 1);
INSERT INTO `player_skills` VALUES (1300, 3486, 1);
INSERT INTO `player_skills` VALUES (1300, 3487, 1);
INSERT INTO `player_skills` VALUES (1300, 3488, 1);
INSERT INTO `player_skills` VALUES (1300, 3489, 1);
INSERT INTO `player_skills` VALUES (1300, 3490, 1);
INSERT INTO `player_skills` VALUES (1300, 3491, 1);
INSERT INTO `player_skills` VALUES (1300, 3496, 1);
INSERT INTO `player_skills` VALUES (1300, 3497, 1);
INSERT INTO `player_skills` VALUES (1300, 3498, 1);
INSERT INTO `player_skills` VALUES (1300, 3499, 1);
INSERT INTO `player_skills` VALUES (1300, 3500, 1);
INSERT INTO `player_skills` VALUES (1300, 3501, 1);
INSERT INTO `player_skills` VALUES (1300, 3502, 1);
INSERT INTO `player_skills` VALUES (1300, 3503, 1);
INSERT INTO `player_skills` VALUES (1300, 3504, 1);
INSERT INTO `player_skills` VALUES (1300, 3505, 1);
INSERT INTO `player_skills` VALUES (1300, 3512, 1);
INSERT INTO `player_skills` VALUES (1300, 3513, 1);
INSERT INTO `player_skills` VALUES (1300, 3514, 1);
INSERT INTO `player_skills` VALUES (1300, 3515, 1);
INSERT INTO `player_skills` VALUES (1300, 3516, 1);
INSERT INTO `player_skills` VALUES (1300, 3517, 1);
INSERT INTO `player_skills` VALUES (1300, 3518, 1);
INSERT INTO `player_skills` VALUES (1300, 3519, 1);
INSERT INTO `player_skills` VALUES (1300, 3520, 1);
INSERT INTO `player_skills` VALUES (1300, 3521, 1);
INSERT INTO `player_skills` VALUES (1300, 3522, 1);
INSERT INTO `player_skills` VALUES (1300, 3523, 1);
INSERT INTO `player_skills` VALUES (1300, 3524, 1);
INSERT INTO `player_skills` VALUES (1300, 3525, 1);
INSERT INTO `player_skills` VALUES (1300, 3526, 1);
INSERT INTO `player_skills` VALUES (1300, 3527, 1);
INSERT INTO `player_skills` VALUES (1300, 3528, 1);
INSERT INTO `player_skills` VALUES (1300, 3529, 1);
INSERT INTO `player_skills` VALUES (1300, 3530, 1);
INSERT INTO `player_skills` VALUES (1300, 30003, 1);
INSERT INTO `player_skills` VALUES (1300, 40009, 1);

-- ----------------------------
-- Table structure for player_surveys
-- ----------------------------
DROP TABLE IF EXISTS `player_surveys`;
CREATE TABLE `player_surveys`  (
  `survey_id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` int(11) NOT NULL,
  `option_id` tinyint(1) NOT NULL,
  PRIMARY KEY (`survey_id`, `player_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Fixed;

-- ----------------------------
-- Records of player_surveys
-- ----------------------------
INSERT INTO `player_surveys` VALUES (1, 1217, 1);

-- ----------------------------
-- Table structure for player_titles
-- ----------------------------
DROP TABLE IF EXISTS `player_titles`;
CREATE TABLE `player_titles`  (
  `player_id` int(11) NOT NULL,
  `title_id` int(11) NOT NULL,
  `title_expires_time` bigint(20) NOT NULL DEFAULT 0,
  `title_date` timestamp NOT NULL DEFAULT '2010-01-01 01:00:01',
  PRIMARY KEY (`player_id`, `title_id`) USING BTREE,
  CONSTRAINT `player_titles_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_titles
-- ----------------------------

-- ----------------------------
-- Table structure for player_web_rewards
-- ----------------------------
DROP TABLE IF EXISTS `player_web_rewards`;
CREATE TABLE `player_web_rewards`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `item_count` int(11) NOT NULL DEFAULT 1,
  `order_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `added` timestamp NOT NULL DEFAULT current_timestamp(),
  `received` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `player_id`(`player_id`) USING BTREE,
  INDEX `received`(`received`) USING BTREE,
  CONSTRAINT `player_web_rewards_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_web_rewards
-- ----------------------------

-- ----------------------------
-- Table structure for player_world_bans
-- ----------------------------
DROP TABLE IF EXISTS `player_world_bans`;
CREATE TABLE `player_world_bans`  (
  `player` int(11) NOT NULL,
  `by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `duration` bigint(11) NOT NULL,
  `date` bigint(11) NOT NULL,
  `reason` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`player`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_world_bans
-- ----------------------------

-- ----------------------------
-- Table structure for players
-- ----------------------------
DROP TABLE IF EXISTS `players`;
CREATE TABLE `players`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `account_id` int(11) NOT NULL,
  `account_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `exp` bigint(20) NOT NULL DEFAULT 0,
  `recoverexp` bigint(20) NOT NULL DEFAULT 0,
  `x` float NOT NULL,
  `y` float NOT NULL,
  `z` float NOT NULL,
  `heading` int(11) NOT NULL,
  `world_id` int(11) NOT NULL,
  `gender` enum('MALE','FEMALE') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `race` enum('ASMODIANS','ELYOS') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `player_class` enum('WARRIOR','GLADIATOR','TEMPLAR','SCOUT','ASSASSIN','RANGER','MAGE','SORCERER','SPIRIT_MASTER','PRIEST','CLERIC','CHANTER') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `creation_date` timestamp NULL DEFAULT NULL,
  `deletion_date` timestamp NULL DEFAULT NULL,
  `last_online` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `cube_size` tinyint(1) NOT NULL DEFAULT 0,
  `advenced_stigma_slot_size` tinyint(1) NOT NULL DEFAULT 0,
  `warehouse_size` tinyint(1) NOT NULL DEFAULT 0,
  `mailboxLetters` tinyint(4) NOT NULL DEFAULT 0,
  `bind_point` int(11) NOT NULL DEFAULT 0,
  `title_id` int(3) NOT NULL DEFAULT -1,
  `visual_title` int(3) NOT NULL DEFAULT -1,
  `online` tinyint(1) NOT NULL DEFAULT 0,
  `note` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name_unique`(`name`) USING BTREE,
  INDEX `idx_account_id`(`account_id`) USING BTREE,
  INDEX `idx_players_account_id`(`account_id`) USING BTREE,
  INDEX `idx_players_online`(`online`) USING BTREE,
  CONSTRAINT `players_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1301 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of players
-- ----------------------------
INSERT INTO `players` VALUES (1221, 'Afaf', 3, 'test22', **********, 0, 2936.81, 823.646, 569.625, 108, *********, 'FEMALE', 'ELYOS', 'ASSASSIN', '2025-07-21 05:50:47', NULL, '2025-07-21 06:04:30', 12, 6, 0, 1, 55, -1, -1, 0, NULL);
INSERT INTO `players` VALUES (1224, 'Vaz', 3, 'test22', 0, 0, 2937.7, 882.4, 573.7, 35, *********, 'FEMALE', 'ELYOS', 'MAGE', '2025-07-22 02:09:04', NULL, '2025-07-22 02:09:06', 12, 0, 0, 0, 55, -1, -1, 1, NULL);
INSERT INTO `players` VALUES (1232, 'Tata', 3, 'test22', **********, 0, 2925.6, 829.481, 569.443, 0, *********, 'FEMALE', 'ELYOS', 'SORCERER', '2025-07-21 22:35:31', NULL, '2025-07-22 02:07:39', 12, 6, 0, 0, 55, -1, -1, 0, NULL);
INSERT INTO `players` VALUES (1300, 'Ava', 3, 'test22', **********, 0, 2926.7, 833.826, 569.544, 38, *********, 'FEMALE', 'ELYOS', 'ASSASSIN', '2025-07-21 06:05:46', NULL, '2025-07-22 02:08:42', 12, 6, 0, 0, 55, -1, -1, 0, NULL);

-- ----------------------------
-- Table structure for price_type
-- ----------------------------
DROP TABLE IF EXISTS `price_type`;
CREATE TABLE `price_type`  (
  `price_id` int(11) NOT NULL AUTO_INCREMENT,
  `price_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `symbolic` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`price_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of price_type
-- ----------------------------
INSERT INTO `price_type` VALUES (1, 'Might Points', 'MP');
INSERT INTO `price_type` VALUES (2, 'Donation Points', 'DP');
INSERT INTO `price_type` VALUES (3, 'Event Points', 'EP');

-- ----------------------------
-- Table structure for server_variables
-- ----------------------------
DROP TABLE IF EXISTS `server_variables`;
CREATE TABLE `server_variables`  (
  `key` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `value` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of server_variables
-- ----------------------------
INSERT INTO `server_variables` VALUES ('abyss', '1753150066967');
INSERT INTO `server_variables` VALUES ('time', '3757');
INSERT INTO `server_variables` VALUES ('weekly', '1753062036600');

-- ----------------------------
-- Table structure for shop_categories
-- ----------------------------
DROP TABLE IF EXISTS `shop_categories`;
CREATE TABLE `shop_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `icon` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `display_order` int(11) NULL DEFAULT 0,
  `is_active` tinyint(1) NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shop_categories
-- ----------------------------
INSERT INTO `shop_categories` VALUES (1, 'Items', '?', 1, 1);
INSERT INTO `shop_categories` VALUES (2, 'Skins', '?', 2, 1);
INSERT INTO `shop_categories` VALUES (3, 'Consumables', '?', 3, 1);

-- ----------------------------
-- Table structure for shop_items
-- ----------------------------
DROP TABLE IF EXISTS `shop_items`;
CREATE TABLE `shop_items`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `price` int(11) NOT NULL,
  `quantity` int(11) NULL DEFAULT 1,
  `image_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `category_id` int(11) NULL DEFAULT 1,
  `rarity` enum('Common','Superior','Heroic','Fabled','Eternal','Mythic','Ancient','Legendary','Ultimate') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'Common',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shop_items
-- ----------------------------
INSERT INTO `shop_items` VALUES (1, 164000073, 'Test Reward Item', 'A special test item to verify the shop is working! This item will be delivered to your character\'s in-game mail.', 10, 5, 'https://imgur.com/LyTnJlp', 1, 'Common', '2025-07-20 22:42:53');
INSERT INTO `shop_items` VALUES (2, 186000236, 'Kinah Bundle (1000)', '1000 Kinah for your adventures', 5, 1, 'images/items/186000236_1753142381.png', 1, 'Common', '2025-07-20 22:42:53');
INSERT INTO `shop_items` VALUES (3, 100000001, 'Premium Health Potion', 'Restores health instantly', 15, 10, '0', 3, 'Superior', '2025-07-20 22:42:53');

-- ----------------------------
-- Table structure for shop_purchases
-- ----------------------------
DROP TABLE IF EXISTS `shop_purchases`;
CREATE TABLE `shop_purchases`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `char_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` bigint(20) NOT NULL DEFAULT 1,
  `gift` tinyint(1) NOT NULL DEFAULT 0,
  `gifter` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `added` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shop_purchases
-- ----------------------------
INSERT INTO `shop_purchases` VALUES (1, 1221, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (2, 1221, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (3, 1221, 186000236, 1, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (4, 1221, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (5, 1221, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (6, 1221, 164000073, 5, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (7, 1232, 164000073, 5, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (8, 1232, 164000073, 5, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (9, 1232, 186000236, 1, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (10, 1232, 186000236, 1, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (11, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (12, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (13, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (14, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (15, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (16, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (17, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (18, 1232, 164000073, 5, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (19, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (20, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (21, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (22, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (23, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (24, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (25, 1232, 100000001, 10, 0, '', 1);
INSERT INTO `shop_purchases` VALUES (26, 1232, 100000001, 10, 0, '', 1);

-- ----------------------------
-- Table structure for shop_removals
-- ----------------------------
DROP TABLE IF EXISTS `shop_removals`;
CREATE TABLE `shop_removals`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `itemUniqueId` int(11) NOT NULL,
  `itemOwner` int(11) NOT NULL,
  `amount` int(11) NOT NULL DEFAULT 1,
  `removed` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shop_removals
-- ----------------------------

-- ----------------------------
-- Table structure for shopcategories
-- ----------------------------
DROP TABLE IF EXISTS `shopcategories`;
CREATE TABLE `shopcategories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shopcategories
-- ----------------------------

-- ----------------------------
-- Table structure for shopitems
-- ----------------------------
DROP TABLE IF EXISTS `shopitems`;
CREATE TABLE `shopitems`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `categoryid` int(11) NOT NULL,
  `itemId` int(11) NOT NULL,
  `count` int(11) NOT NULL,
  `price` int(11) NULL DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` enum('NORMAL','NEW','HOT') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'NORMAL',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shopitems
-- ----------------------------

-- ----------------------------
-- Table structure for shoplog
-- ----------------------------
DROP TABLE IF EXISTS `shoplog`;
CREATE TABLE `shoplog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` timestamp NULL DEFAULT NULL,
  `accountId` int(11) NOT NULL,
  `itemId` int(11) NOT NULL,
  `count` int(11) NOT NULL,
  `price` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Fixed;

-- ----------------------------
-- Records of shoplog
-- ----------------------------

-- ----------------------------
-- Table structure for siege_locations
-- ----------------------------
DROP TABLE IF EXISTS `siege_locations`;
CREATE TABLE `siege_locations`  (
  `id` int(11) NOT NULL,
  `race` enum('ELYOS','ASMODIANS','BALAUR') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `legion_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of siege_locations
-- ----------------------------
INSERT INTO `siege_locations` VALUES (7011, 'BALAUR', 0);
INSERT INTO `siege_locations` VALUES (7012, 'BALAUR', 0);
INSERT INTO `siege_locations` VALUES (7013, 'BALAUR', 0);
INSERT INTO `siege_locations` VALUES (7014, 'BALAUR', 0);
INSERT INTO `siege_locations` VALUES (210071, 'ELYOS', 0);
INSERT INTO `siege_locations` VALUES (210072, 'ELYOS', 0);
INSERT INTO `siege_locations` VALUES (210073, 'ELYOS', 0);
INSERT INTO `siege_locations` VALUES (220081, 'ASMODIANS', 0);
INSERT INTO `siege_locations` VALUES (220082, 'ASMODIANS', 0);
INSERT INTO `siege_locations` VALUES (220083, 'ASMODIANS', 0);

-- ----------------------------
-- Table structure for siege_log
-- ----------------------------
DROP TABLE IF EXISTS `siege_log`;
CREATE TABLE `siege_log`  (
  `log_uuid` bigint(20) NOT NULL AUTO_INCREMENT,
  `legion_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `action` enum('CAPTURE','DEFEND') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `tstamp` bigint(20) NOT NULL,
  `siegeloc_id` bigint(20) NOT NULL,
  PRIMARY KEY (`log_uuid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of siege_log
-- ----------------------------

-- ----------------------------
-- Table structure for spawn_groups
-- ----------------------------
DROP TABLE IF EXISTS `spawn_groups`;
CREATE TABLE `spawn_groups`  (
  `admin_id` int(11) NOT NULL,
  `group_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `spawned` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`admin_id`, `group_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of spawn_groups
-- ----------------------------

-- ----------------------------
-- Table structure for spawns
-- ----------------------------
DROP TABLE IF EXISTS `spawns`;
CREATE TABLE `spawns`  (
  `spawn_id` int(11) NOT NULL AUTO_INCREMENT,
  `object_id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `group_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `npc_id` int(11) NOT NULL,
  `respawn` tinyint(1) NOT NULL DEFAULT 0,
  `map_id` int(11) NOT NULL,
  `x` float NOT NULL,
  `y` float NOT NULL,
  `z` float NOT NULL,
  `h` tinyint(8) NOT NULL,
  `spawned` tinyint(1) NOT NULL DEFAULT 0,
  `staticid` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`spawn_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of spawns
-- ----------------------------

-- ----------------------------
-- Table structure for surveys
-- ----------------------------
DROP TABLE IF EXISTS `surveys`;
CREATE TABLE `surveys`  (
  `survey_id` int(11) NOT NULL AUTO_INCREMENT,
  `owner_id` int(11) NOT NULL,
  `title` varchar(127) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `message` varchar(1023) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `itemId` int(11) NULL DEFAULT NULL,
  `itemCount` int(11) NULL DEFAULT NULL,
  `player_level_min` tinyint(1) NULL DEFAULT NULL,
  `player_level_max` tinyint(1) NULL DEFAULT NULL,
  `survey_all` tinyint(1) NULL DEFAULT NULL,
  PRIMARY KEY (`survey_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of surveys
-- ----------------------------

-- ----------------------------
-- Table structure for surveys_option
-- ----------------------------
DROP TABLE IF EXISTS `surveys_option`;
CREATE TABLE `surveys_option`  (
  `survey_id` int(11) NOT NULL AUTO_INCREMENT,
  `option_id` tinyint(1) NOT NULL,
  `option_text` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `itemId` int(11) NULL DEFAULT NULL,
  `itemCount` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`survey_id`, `option_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of surveys_option
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
