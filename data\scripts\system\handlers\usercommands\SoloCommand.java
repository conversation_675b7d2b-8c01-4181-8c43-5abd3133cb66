/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.LadderService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.util.Map;

import javolution.util.FastMap;

/**
 * <AUTHOR>
 */
public class SoloCommand extends UserCommand {
    private static Map<Integer, Long> nextUse = new FastMap<Integer, Long>();

    private static final int REGISTRATION_DELAY = 8 * 60 * 1000;

    public SoloCommand() {
        super("1v1");
    }

    public void executeCommand(Player player, String param) {
        @SuppressWarnings("unused")
        String[] params = param.split(" ");

        if (!LadderService.getInstance().isInQueue(player)) {
            /*
             * if (nextUse.containsKey(player.getObjectId()) && nextUse.get(player.getObjectId()) >
             * System.currentTimeMillis()) { int secondsLeft = (int) ((nextUse.get(player.getObjectId()) - System
             * .currentTimeMillis()) / 1000); announce(player,
             * "You cannot register for the 1v1 queue again yet! Please wait " + secondsLeft + " seconds."); return; }
             */

            if (LadderService.getInstance().registerForSolo(player)) {
                announce(player, "You have registered to the 1v1 queue!");
                // nextUse.put(player.getObjectId(), System.currentTimeMillis() + REGISTRATION_DELAY);
            }
            else {
                announce(player, "You have failed to register for the 1v1 queue.");
            }
        }
        else {
            LadderService.getInstance().unregisterFromQueue(player);
            announce(player, "You have unregistered from the 1v1 queue!");
        }
    }

    private void announce(Player player, String msg) {
        PacketSendUtility.sendSys2Message(player, "1v1", msg);
    }
}