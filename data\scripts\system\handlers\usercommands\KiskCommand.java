/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.Kisk;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_KISK_UPDATE;
import gameserver.services.ArenaService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.util.Map;

import javolution.util.FastMap;

/**
 * 
 * <AUTHOR>
 */
public class KiskCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new FastMap<Integer, Long>();

    public KiskCommand() {
        super("kisk");
    }

    public void executeCommand(final Player player, String param) {
        String[] params = param.split(" ");

        if (player.getBattleground() != null || ArenaService.getInstance().isInArena(player)) {
            PacketSendUtility
                .sendMessage(player, "You cannot use this command while in BG or FFA!");
            return;
        }
        else if (player.isInCombatLong()) {
            PacketSendUtility.sendMessage(player, "You cannot use this command while in combat!");
            return;
        }
        else if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 10000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 10 seconds!");
                return;
            }
        }

        if (player.getKisk() != null) {
            Kisk kisk = player.getKisk();

            if (kisk.getOwnerObjectId() != player.getObjectId()) {
                kisk.removePlayer(player);
                PacketSendUtility.sendPacket(player, new SM_KISK_UPDATE(kisk, 0));
                PacketSendUtility.sendMessage(player,
                    "You cannot kill a kisk you don't own, but you have been removed from it!");
            }
            else {
                kisk.getController().onDie(kisk);
                PacketSendUtility.sendMessage(player, "Your kisk has been terminated!");
            }
        }
        else {
            PacketSendUtility.sendMessage(player, "You currently have no kisk.");
        }

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }
}