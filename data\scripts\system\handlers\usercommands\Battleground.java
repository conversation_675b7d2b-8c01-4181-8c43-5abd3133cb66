/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.LadderService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

/**
 * 
 * <AUTHOR>
 */
public class Battleground extends UserCommand {
    public Battleground() {
        super("bg");
    }

    public void executeCommand(Player player, String param) {
        @SuppressWarnings("unused")
        String[] params = param.split(" ");

        if (player.isTemporary())
            return;

        if (!LadderService.getInstance().isNormalReady()) {
            PacketSendUtility.sendMessage(player,
                "Please wait until the next round of battlegrounds before registering.");
            return;
        }

        if (!LadderService.getInstance().isInQueue(player)) {
            if (LadderService.getInstance().registerForNormal(player))
                announce(player, "You have registered for an upcoming battleground!");
            else
                announce(player, "You cannot register while you're in a battleground or while spectating.");
        }
        else {
            LadderService.getInstance().unregisterFromQueue(player);
            announce(player, "You have unregistered from the Battleground queue. Have a nice day!");
        }
    }

    private void announce(Player player, String msg) {
        PacketSendUtility.sendSys2Message(player, "BG", msg);
    }
}