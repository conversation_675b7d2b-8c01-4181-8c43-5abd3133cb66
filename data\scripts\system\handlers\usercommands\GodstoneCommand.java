/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_UPDATE_PLAYER_APPEARANCE;
import gameserver.services.ArenaService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.util.Map;

import javolution.util.FastMap;

/**
 * 
 * <AUTHOR>
 */
public class GodstoneCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new FastMap<Integer, Long>();

    public GodstoneCommand() {
        super("godstone");
    }

    public void executeCommand(final Player player, String param) {
        String[] params = param.split(" ");

        if (player.getBattleground() != null || ArenaService.getInstance().isInArena(player)) {
            PacketSendUtility
                .sendMessage(player, "You cannot use this command while in BG or FFA!");
            return;
        }
        else if (player.isInCombatLong()) {
            PacketSendUtility.sendMessage(player, "You cannot use this command while in combat!");
            return;
        }
        else if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 10000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 10 seconds!");
                return;
            }
        }

        Item main = player.getEquipment().getMainHandWeapon();
        if (main != null) {
            main.setGodstoneGlow(main.isGodstoneGlow() ? false : true);
        }

        Item off = player.getEquipment().getOffHandWeapon();
        if (off != null) {
            off.setGodstoneGlow(off.isGodstoneGlow() ? false : true);
        }

        PacketSendUtility.broadcastPacketAndReceive(player,
            new SM_UPDATE_PLAYER_APPEARANCE(player.getObjectId(), player.getEquipment()
                .getEquippedItemsWithoutStigma()));

        PacketSendUtility.sendMessage(player,
            "You have switched the Godstone on your currently equipped weapon"
                + (off != null ? "s" : "") + "!");

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }
}