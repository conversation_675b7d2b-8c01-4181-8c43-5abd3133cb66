/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.TaskId;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.Battleground;
import gameserver.services.ArenaService;
import gameserver.services.LadderService;
import gameserver.services.OpenWorldService;
import gameserver.services.TeleportService;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.UserCommand;
import gameserver.world.World;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import javolution.util.FastMap;

import com.aionemu.commons.utils.Rnd;

/**
 * 
 * <AUTHOR>
 */
public class Spectate extends UserCommand {
    private static Map<Integer, Long> lastExecute = new FastMap<Integer, Long>();

    public Spectate() {
        super("spectate");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 4000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 4 seconds!");
                return;
            }
        }

        if (params[0].startsWith("list")) {
            Map<Integer, Battleground> bgMap = LadderService.getInstance().getBattlegrounds();

            if (bgMap == null) {
                message(player, "An error has occured!");
                return;
            }

            String msg = "--- LIVE BATTLEGROUND LIST ---\n";
            if (bgMap.size() > 0) {
                for (Map.Entry<Integer, Battleground> entry : bgMap.entrySet()) {
                    Battleground bg = entry.getValue();

                    if (bg.isTournament())
                        msg += "[TOURNAMENT] ";
                    if (bg.isEvent())
                        msg += "[EVENT] ";
                    if (bg.is1v1())
                        msg += "[1v1] ";
                    if (bg.is2v2())
                        msg += "[2v2] ";
                    if (bg.isPvE())
                        msg += "[PvE] ";

                    if (player.getAccessLevel() >= 5 && !bg.isPvE() && !bg.is1v1() && !bg.is2v2())
                        msg += "[R" + bg.getAverageParticipantRating() + "] ";

                    msg += bg.getName() + " (id: " + entry.getKey() + ") ";
                    msg += "with " + secondsToString(bg.getSecondsLeft()) + " left\n";
                }
            }
            else {
                msg += "There are currently no battlegrounds running!\n";
            }
            msg += "--- END OF LIVE LIST ---";

            message(player, msg);
        }
        else if (params[0].startsWith("pve")) {
            Map<Integer, Battleground> bgMap = LadderService.getInstance().getBattlegrounds();

            if (bgMap == null) {
                message(player, "An error has occured!");
                return;
            }

            String msg = "--- LIVE PVE LIST ---\n";
            if (bgMap.size() > 0) {
                for (Map.Entry<Integer, Battleground> entry : bgMap.entrySet()) {
                    Battleground bg = entry.getValue();

                    if (!bg.isPvE())
                        continue;

                    if (bg.isTournament())
                        msg += "[TOURNAMENT] ";
                    if (bg.isEvent())
                        msg += "[EVENT] ";
                    if (bg.isPvE())
                        msg += "[PvE] ";

                    msg += bg.getName() + " (id: " + entry.getKey() + ") ";
                    msg += "with " + secondsToString(bg.getSecondsLeft()) + " left\n";
                }
            }
            else {
                msg += "There are currently no battlegrounds running!\n";
            }
            msg += "--- END OF LIVE LIST ---";

            message(player, msg);
        }
        else if (params[0].startsWith("pvp")) {
            Map<Integer, Battleground> bgMap = LadderService.getInstance().getBattlegrounds();

            if (bgMap == null) {
                message(player, "An error has occured!");
                return;
            }

            String msg = "--- LIVE PVP LIST ---\n";
            if (bgMap.size() > 0) {
                for (Map.Entry<Integer, Battleground> entry : bgMap.entrySet()) {
                    Battleground bg = entry.getValue();

                    if (bg.isPvE())
                        continue;

                    if (bg.isTournament())
                        msg += "[TOURNAMENT] ";
                    if (bg.isEvent())
                        msg += "[EVENT] ";
                    if (bg.is1v1())
                        msg += "[1v1] ";
                    if (bg.is2v2())
                        msg += "[2v2] ";

                    if (player.getAccessLevel() >= 5 && !bg.isPvE() && !bg.is1v1() && !bg.is2v2())
                        msg += "[R" + bg.getAverageParticipantRating() + "] ";

                    msg += bg.getName() + " (id: " + entry.getKey() + ") ";
                    msg += "with " + secondsToString(bg.getSecondsLeft()) + " left\n";
                }
            }
            else {
                msg += "There are currently no battlegrounds running!\n";
            }
            msg += "--- END OF LIVE LIST ---";

            message(player, msg);
        }
        else if (params[0].startsWith("join")) {
            if (LadderService.getInstance().isInQueue(player)
                || (player.getBattleground() != null && !player.isSpectating())) {
                message(player, "You cannot spectate matches while in the queue or in a match.");
                return;
            }
            else if (params.length < 2) {
                message(player,
                    "Syntax: .spectate join <id | player name> - get the id from .spectate list");
                return;
            }
            else if (ArenaService.getInstance().isInArena(player)) {
                message(player, "You must first leave the FFA map to spectate.");
                return;
            }
            else if (player.isInCombatLong()) {
                message(player, "You cannot spectate matches while in combat.");
                return;
            }
            else if (player.getController().hasActiveTask(TaskId.TELEPORT)) {
                message(player, "You cannot spectate matches while teleporting.");
                return;
            }

            if (OpenWorldService.getInstance().isInOpenWorld(player)) {
                for (Player pl : player.getKnownList().getPlayers()) {
                    if (pl.isEnemy(player) && MathUtil.isIn3dRange(pl, player, 50)) {
                        message(player,
                            "You cannot spectate matches in the near presence of enemy players.");
                        return;
                    }
                }
            }

            Map<Integer, Battleground> bgMap = LadderService.getInstance().getBattlegrounds();

            Integer bgId;
            try {
                bgId = Integer.parseInt(params[1]);

                if (bgMap.containsKey(bgId)) {
                    if (player.getBattleground() != null && player.isSpectating())
                        player.getBattleground().onSpectatorSoftLeave(player);

                    bgMap.get(bgId).onSpectatorJoin(player);

                    lastExecute.put(player.getObjectId(), System.currentTimeMillis());
                }
                else
                    message(player, "There is no battleground running with that id!");
            }
            catch (Exception e) {
                String name = Util.convertName(params[1]);
                Player pl = World.getInstance().findPlayer(name);

                if (pl == null) {
                    message(player, "There is no player named " + params[1] + " online.");
                    return;
                }
                else if (pl.getBattleground() == null) {
                    message(player, "The specified player is not in a battleground.");
                    return;
                }
                else if (pl.getBattleground().getSecondsLeft() < 2) {
                    message(player, "You cannot spectate the battleground as it is ending already.");
                    return;
                }

                if (player.getBattleground() != null && player.isSpectating())
                    player.getBattleground().onSpectatorSoftLeave(player);

                pl.getBattleground().onSpectatorJoin(player);

                lastExecute.put(player.getObjectId(), System.currentTimeMillis());
            }
        }
        else if (params[0].startsWith("leave")) {
            if (ArenaService.getInstance().isInArena(player) && player.isSpectating()) {
                message(player, "You are now leaving FFA.");
                ArenaService.getInstance().spectatorLeave(player);
                return;
            }

            if (player.getBattleground() == null || !player.isSpectating()) {
                message(player, "You're not spectating a battleground.");
                return;
            }

            message(player, "You are now leaving the battleground!");
            player.getBattleground().onSpectatorLeave(player, false);

            lastExecute.put(player.getObjectId(), System.currentTimeMillis());
        }
        else if (params[0].startsWith("ffa")) {
            if (ArenaService.getInstance().isInArena(player)) {
                if (!player.isSpectating()) {
                    message(player, "You cannot spectate the FFA map while already on it.");
                    return;
                }

                message(player, "You are now leaving FFA.");
                ArenaService.getInstance().spectatorLeave(player);

                lastExecute.put(player.getObjectId(), System.currentTimeMillis());

                return;
            }
            else if (LadderService.getInstance().isInQueue(player)
                || (player.getBattleground() != null && !player.isSpectating())) {
                message(player, "You cannot spectate matches while in the queue or in a match.");
                return;
            }
            else if (player.getBattleground() != null) {
                message(player, "You cannot spectate the FFA map while in a battleground.");
                return;
            }
            else if (player.isInCombatLong()) {
                message(player, "You cannot spectate matches while in combat.");
                return;
            }
            else if (player.getController().hasActiveTask(TaskId.TELEPORT)) {
                message(player, "You cannot spectate matches while teleporting.");
                return;
            }
            
            if (OpenWorldService.getInstance().isInOpenWorld(player)) {
                for (Player pl : player.getKnownList().getPlayers()) {
                    if (pl.isEnemy(player) && MathUtil.isIn3dRange(pl, player, 50)) {
                        message(player,
                            "You cannot spectate matches in the near presence of enemy players.");
                        return;
                    }
                }
            }

            message(player, "You are now entering the FFA map as spectator.");
            ArenaService.getInstance().spectatorJoin(player);

            lastExecute.put(player.getObjectId(), System.currentTimeMillis());
        }
        else if (params[0].startsWith("findplayer")) {
            if (!player.isSpectating()) {
                message(player, "You may only use this command while spectating.");
                return;
            }
            else if (!player.isSpawned()) {
                message(player, "Please wait until you're spawned before using this command.");
                return;
            }

            Collection<Player> players = player.getWorldMapInstance().getPlayers(false);

            if (players.isEmpty()) {
                message(player, "There are no players to go to.");
                return;
            }

            List<Player> _players = new ArrayList<Player>(players.size());
            _players.addAll(players);

            Player target = _players.get(Rnd.get(_players.size()));

            message(player, "Teleporting to " + target.getName() + ".");

            TeleportService.teleportTo(player, target.getWorldId(), target.getX(), target.getY(),
                target.getZ(), 0);

            lastExecute.put(player.getObjectId(), System.currentTimeMillis());
        }
        else {
            message(player,
                "Syntax: .spectate <list | join | leave | ffa | findplayer | pve | pvp>");
        }
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendMessage(player, msg);
    }

    private String secondsToString(int pTime) {
        return String.format("%01d:%02d", pTime / 60, pTime % 60);
    }
}