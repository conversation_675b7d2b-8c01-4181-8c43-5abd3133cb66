/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.EventService;
import gameserver.services.LadderService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

/**
 * <AUTHOR>
 */
public class DuoCommand extends UserCommand {
    public DuoCommand() {
        super("2v2");
    }

    public void executeCommand(Player player, String param) {
        @SuppressWarnings("unused")
        String[] params = param.split(" ");

        if (!EventService.getInstance().is2v2Enabled()) {
            announce(player, "The 2v2 queue is currently closed.");
            return;
        }

        if (!player.isInGroup() || player.getPlayerGroup().size() != 2) {
            announce(player, "You must be in a group of 2 to enter the 2v2 queue.");
            return;
        }
        else if (player.getPlayerGroup().getGroupLeader().getObjectId() != player.getObjectId()) {
            announce(player, "Only the group leader can enter the 2v2 queue.");
            return;
        }

        if (!LadderService.getInstance().isInQueue(player.getPlayerGroup())) {
            if (LadderService.getInstance().registerForDuo(player.getPlayerGroup())) {
                announce(player, "You have registered to the 2v2 queue!");
            }
            else {
                announce(player, "You have failed to register for the 2v2 queue.");
            }
        }
        else {
            LadderService.getInstance().unregisterFromQueue(player.getPlayerGroup());
            announce(player, "You have unregistered from the 2v2 queue!");
        }
    }

    private void announce(Player player, String msg) {
        PacketSendUtility.sendSys2Message(player, "2v2", msg);
    }
}