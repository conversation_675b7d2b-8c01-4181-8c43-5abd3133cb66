/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.dao.MightDAO;
import gameserver.dao.PlayerDAO;
import gameserver.model.Race;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 * 
 */
public class MightCommand extends AdminCommand {
    public MightCommand() {
        super("might");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_SETMIGHT) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command!");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //might <add | info | addall>");
            return;
        }

        MightDAO dao = DAOManager.getDAO(MightDAO.class);

        if ("add".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //might add <points> OR\n"
                    + "Syntax: //might add <name> <points>\n"
                    + "Note: You may also use negative numbers!");
                return;
            }

            Player target = null;
            int accountId = 0;
            int points;

            if (params.length > 2) {
                accountId = DAOManager.getDAO(PlayerDAO.class).getAccountIdByName(
                    Util.convertName(params[1]));
                points = Integer.parseInt(params[2]);

                if (accountId == 0) {
                    PacketSendUtility.sendMessage(admin, "The specified player does not exist!");
                    return;
                }

                target = World.getInstance().findPlayer(Util.convertName(params[1]));
            }
            else {
                if (admin.getTarget() != null && admin.getTarget() instanceof Player) {
                    accountId = ((Player) admin.getTarget()).getPlayerAccount().getId();
                    points = Integer.parseInt(params[1]);
                }
                else {
                    PacketSendUtility.sendMessage(admin, "Please select a player as your target!");
                    return;
                }

                target = (Player) admin.getTarget();
            }

            if (dao.addMight(accountId, points)) {
                if (target != null) {
                    PacketSendUtility
                        .sendMessage(target, "You have received " + points + " might!");
                    PacketSendUtility.sendMessage(admin,
                        "Added " + points + " might to " + target.getName() + "!");
                }
                else {
                    PacketSendUtility.sendMessage(admin, "Added " + points + " might to "
                        + params[1] + "!");
                }
            }
            else {
                if (target != null)
                    PacketSendUtility.sendMessage(admin,
                        "An error occured while adding the might to " + target.getName());
                else
                    PacketSendUtility.sendMessage(admin,
                        "An error occured while adding the might to " + params[1]);
            }
        }
        else if ("info".startsWith(params[0])) {
            Player target = null;

            if (params.length >= 2) {
                target = World.getInstance().findPlayer(Util.convertName(params[1]));
            }
            else if (admin.getTarget() != null && admin.getTarget() instanceof Player) {
                target = (Player) admin.getTarget();
            }
            else {
                PacketSendUtility.sendMessage(admin,
                    "Syntax: //might info <name> -- OR target without <name>");
                return;
            }

            if (target == null) {
                int accountId = DAOManager.getDAO(PlayerDAO.class).getAccountIdByName(
                    Util.convertName(params[1]));

                if (accountId == 0) {
                    PacketSendUtility.sendMessage(admin, "The specified player does not exist!");
                    return;
                }

                PacketSendUtility.sendMessage(admin,
                    Util.convertName(params[1]) + " has " + dao.getMight(accountId)
                        + " might on his account.");
                return;
            }

            PacketSendUtility.sendMessage(admin, target.getName() + " has " + dao.getMight(target)
                + " might on his account.");
        }
        else if ("addall".startsWith(params[0])) {
            if (params.length < 3) {
                PacketSendUtility.sendMessage(admin,
                    "Syntax: //might addall <all | map | ely | asmo> <points>\n"
                        + "CAUTION: THIS ADDS MIGHT TO ALL ONLINE PLAYERS!");
                return;
            }

            int points = Integer.parseInt(params[2]);

            if ("all".equalsIgnoreCase(params[1])) {
                for (Player pl : World.getInstance().getPlayers()) {
                    PacketSendUtility.sendMessage(pl, "You have received " + points + " might!");
                    dao.addMight(pl, points);
                }
            }
            else if ("map".equalsIgnoreCase(params[1])) {
                for (Player pl : admin.getWorldMapInstance().getPlayers()) {
                    if (pl.isSpectating())
                        continue;
                    
                    PacketSendUtility.sendMessage(pl, "You have received " + points + " might!");
                    dao.addMight(pl, points);
                }
            }
            else {
                Race race = null;
                if ("elyos".startsWith(params[1].toLowerCase())) {
                    race = Race.ELYOS;
                }
                else if ("asmodians".startsWith(params[1].toLowerCase())) {
                    race = Race.ASMODIANS;
                }
                else {
                    PacketSendUtility
                        .sendMessage(admin,
                            "Please specify \"all\" or a race! Race names can be shortened down to \"a\" and \"e\".");
                    return;
                }

                for (Player pl : World.getInstance().getPlayers()) {
                    if (pl.getCommonData().getRace() != race)
                        continue;

                    PacketSendUtility.sendMessage(pl, "You have received " + points + " might!");
                    dao.addMight(pl, points);
                }
            }

            PacketSendUtility.sendMessage(admin,
                "The might has been awarded to all online players.");
        }
    }
}
